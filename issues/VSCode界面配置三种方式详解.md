# VSCode界面配置 mcp-feedback-enhanced 三种方式详解

## 前置准备

### 环境检查
```bash
# 检查必要工具是否安装
node --version    # 确保Node.js已安装
npm --version     # 确保npm已安装
python --version  # 确保Python已安装
pip --version     # 确保pip已安装
uv --version      # 检查uv是否安装
```

### 如果缺少工具，先安装：
```bash
# 安装uv（推荐方法）
curl -LsSf https://astral.sh/uv/install.sh | sh
# Windows用户
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 重启终端使路径生效
```

---

## 方式一：Command (stdio) 配置（最推荐）

### 适用场景
- 需要最大灵活性和控制权
- 想要使用uvx来管理Python包
- 对命令行工具比较熟悉

### 详细步骤

#### 步骤1：选择配置类型
1. 打开VSCode，启动GitHub Copilot Chat
2. 切换到Agent模式
3. 点击🔧工具图标
4. 滚动到底部，点击"Add More Tools..."
5. 点击"Add MCP Server"
6. **选择 "Command (stdio)"**

#### 步骤2：基础配置
1. **Server Name（服务器名称）**
   ```
   mcp-feedback-enhanced
   ```

2. **Command（命令）**
   ```
   uvx
   ```

3. **Arguments（参数）**
   ```json
   [
     "mcp-feedback-enhanced@latest"
   ]
   ```

#### 步骤3：环境变量配置
**基础配置**：
```json
{
  "MCP_WEB_HOST": "127.0.0.1",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN"
}
```

**桌面模式配置**：
```json
{
  "MCP_DESKTOP_MODE": "true",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN"
}
```

**远程开发配置**：
```json
{
  "MCP_WEB_HOST": "0.0.0.0",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN"
}
```

**完整高级配置**：
```json
{
  "MCP_WEB_HOST": "127.0.0.1",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN",
  "MCP_AUTO_COMMIT": "true",
  "MCP_SESSION_TIMEOUT": "3600",
  "MCP_AUTO_SAVE": "true",
  "MCP_NOTIFICATION": "true"
}
```

#### 步骤4：权限和超时设置
1. **Timeout（超时时间）**：`600` 秒
2. **Auto Approve（自动批准）**：
   ```json
   ["interactive_feedback"]
   ```
   或允许所有工具：
   ```json
   ["*"]
   ```

#### 步骤5：保存和启动
1. 点击**"Save"**保存配置
2. 在服务器列表中找到刚创建的服务器
3. 点击**"Start"**按钮启动服务

#### 步骤6：验证配置
```bash
# 手动测试命令是否正常
uvx mcp-feedback-enhanced@latest test

# 测试Web界面
uvx mcp-feedback-enhanced@latest test --web
```

---

## 方式二：NPM Package 配置

### 适用场景
- 想要通过npm管理包
- 需要npm生态系统集成
- 偏好Node.js工具链

### 详细步骤

#### 步骤1：预安装准备
```bash
# 首先通过npm全局安装（可选，但推荐）
npm install -g @modelcontextprotocol/server-github
# 或者确保能通过npx运行
npx --version
```

#### 步骤2：选择配置类型
1. 在MCP服务器类型选择界面
2. **选择 "NPM Package"**

#### 步骤3：包配置
1. **Server Name（服务器名称）**
   ```
   mcp-feedback-enhanced-npm
   ```

2. **Package Name（包名）**
   ```
   mcp-feedback-enhanced
   ```
   **注意**：如果这个包在npm中不存在，可能需要使用其他方式

3. **Version（版本）**
   ```
   latest
   ```

#### 步骤4：环境变量（同Command方式）
```json
{
  "MCP_WEB_HOST": "127.0.0.1",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN"
}
```

#### 步骤5：权限设置
- **Timeout**: `600`
- **Auto Approve**: `["interactive_feedback"]`

#### 步骤6：备用配置（如果npm包不可用）
如果直接的npm包不可用，可以尝试：

1. **使用wrapper包**（如果存在）
2. **回退到Command方式**，使用npx：
   - Command: `npx`
   - Arguments: `["uvx", "mcp-feedback-enhanced@latest"]`

---

## 方式三：Pip Package 配置

### 适用场景
- 偏好Python包管理
- 系统已有完善的Python环境
- 想要直接使用pip管理依赖

### 详细步骤

#### 步骤1：预安装验证
```bash
# 验证pip可以找到包
pip search mcp-feedback-enhanced
# 或者尝试安装（测试）
pip install --dry-run mcp-feedback-enhanced
```

#### 步骤2：选择配置类型
1. **选择 "Pip Package"**

#### 步骤3：包配置
1. **Server Name（服务器名称）**
   ```
   mcp-feedback-enhanced-pip
   ```

2. **Package Name（包名）**
   ```
   mcp-feedback-enhanced
   ```

3. **Version（版本）**
   ```
   latest
   ```

#### 步骤4：Python环境配置
确保VSCode使用正确的Python环境：

1. **检查Python路径**
   ```bash
   which python
   which pip
   ```

2. **如果使用虚拟环境**，需要在环境变量中指定：
   ```json
   {
     "PYTHONPATH": "/path/to/your/venv",
     "MCP_WEB_HOST": "127.0.0.1",
     "MCP_WEB_PORT": "8765",
     "MCP_DEBUG": "false",
     "MCP_LANGUAGE": "zh-CN"
   }
   ```

#### 步骤5：环境变量配置
```json
{
  "MCP_WEB_HOST": "127.0.0.1",
  "MCP_WEB_PORT": "8765",
  "MCP_DEBUG": "false",
  "MCP_LANGUAGE": "zh-CN",
  "PYTHON_UNBUFFERED": "1"
}
```

#### 步骤6：权限和启动
- **Timeout**: `600`
- **Auto Approve**: `["interactive_feedback"]`
- 保存并启动服务

#### 步骤7：手动验证
```bash
# 确认包已安装
pip show mcp-feedback-enhanced

# 测试运行
python -m mcp_feedback_enhanced test
```

---

## 配置选择建议

### 推荐优先级

1. **首选：Command (stdio) 方式**
   - ✅ 最灵活，支持所有功能
   - ✅ 使用uvx，自动管理依赖
   - ✅ 跨平台兼容性最好
   - ✅ 更新和维护最简单

2. **次选：Pip Package 方式**
   - ✅ 直接使用Python生态
   - ✅ 适合Python开发者
   - ⚠️ 需要管理Python环境
   - ⚠️ 可能有依赖冲突

3. **备选：NPM Package 方式**
   - ⚠️ 依赖于npm生态中是否有对应包
   - ⚠️ 可能需要额外的wrapper
   - ⚠️ 不是原生支持方式

### 选择标准

**如果你：**
- 👥 团队协作 → 选择 **Command方式**
- 🐍 主要用Python → 选择 **Pip Package**
- 🌐 主要用Node.js → 选择 **NPM Package**
- 🔧 需要最大控制 → 选择 **Command方式**
- 📦 喜欢简单安装 → 选择 **Command方式**

---

## 通用故障排除

### 常见问题解决

#### 1. 服务器无法启动
```bash
# 检查命令是否可用
uvx --version
pip --version
npx --version

# 清理缓存
uv cache clean
npm cache clean --force
pip cache purge
```

#### 2. 端口冲突
```bash
# 检查端口占用
netstat -an | grep 8765
lsof -i :8765  # macOS/Linux

# 在配置中更换端口
"MCP_WEB_PORT": "9876"
```

#### 3. 权限问题
- 确保选择了正确的Auto Approve设置
- 临时使用 `["*"]` 允许所有工具测试

#### 4. 网络连接问题
- 检查防火墙设置
- 确认VSCode可以访问外网
- 尝试使用代理设置（如需要）

### 调试方法

#### 启用详细日志
```json
{
  "MCP_DEBUG": "true",
  "MCP_LOG_LEVEL": "debug"
}
```

#### 查看VSCode输出
1. VSCode → View → Output
2. 选择 "GitHub Copilot Chat" 输出通道
3. 查看MCP相关日志

### 验证成功标准

✅ **配置成功的标志：**
1. MCP服务器状态显示为"Running"
2. 工具列表中出现"interactive_feedback"
3. 浏览器能访问 `http://127.0.0.1:8765`
4. Copilot Chat可以调用反馈工具

选择最适合你环境的配置方式，按照对应的详细步骤进行设置即可。 