# mcp-feedback-enhanced 详细配置指导

## 概述

mcp-feedback-enhanced 是一个增强版MCP服务器，用于AI辅助开发中的交互式用户反馈和命令执行。本指导将详细介绍在VSCode的GitHub Copilot中配置和使用此工具的两种方案。

## 前置准备

### 系统要求
- **VSCode版本**: 1.99 或更高版本
- **GitHub Copilot扩展**: 已安装并激活
- **网络环境**: 能够访问PyPI和GitHub
- **Node.js**: 建议安装最新LTS版本（用于uvx命令）

### 检查环境
```bash
# 检查VSCode版本
code --version

# 检查是否安装了uvx (通过uv安装)
uv --version

# 如果没有安装uv，先安装
curl -LsSf https://astral.sh/uv/install.sh | sh
# 或者在Windows上
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

---

## 方案一：VSCode集成配置（推荐）

### 步骤1：打开MCP配置界面

1. **启动VSCode**并打开你的项目
2. **打开GitHub Copilot Chat**
   - 点击左侧活动栏的Copilot图标
   - 或使用快捷键 `Ctrl+Shift+P`，输入"GitHub Copilot: Open Chat"

3. **切换到Agent模式**
   - 在Copilot Chat界面，点击下拉菜单选择"Agent"
   - 确保显示为Agent模式

4. **打开工具配置**
   - 点击Chat界面左上角的🔧工具图标
   - 滚动到底部，点击"Add More Tools..."

### 步骤2：添加MCP服务器

1. **点击"Add MCP Server"按钮**

2. **选择NPX Package类型**
   - 在弹出的对话框中选择"NPX Package"

3. **输入包信息**
   ```
   Package: mcp-feedback-enhanced@latest
   ```

4. **设置服务器名称**
   ```
   Server Name: mcp-feedback-enhanced
   ```

### 步骤3：配置环境变量

1. **基础配置（Web UI模式）**
   ```json
   {
     "MCP_WEB_HOST": "127.0.0.1",
     "MCP_WEB_PORT": "8765",
     "MCP_DEBUG": "false"
   }
   ```

2. **桌面应用模式配置**
   ```json
   {
     "MCP_DESKTOP_MODE": "true",
     "MCP_WEB_PORT": "8765",
     "MCP_DEBUG": "false"
   }
   ```

3. **高级配置选项**
   ```json
   {
     "MCP_WEB_HOST": "127.0.0.1",
     "MCP_WEB_PORT": "8765",
     "MCP_DEBUG": "false",
     "MCP_LANGUAGE": "zh-CN",
     "MCP_AUTO_COMMIT": "true",
     "MCP_SESSION_TIMEOUT": "3600"
   }
   ```

### 步骤4：设置权限

1. **启用自动批准**
   - 在配置界面找到"Auto Approve"选项
   - 添加工具名称：`interactive_feedback`

2. **设置超时时间**
   - 设置Timeout为：`600`秒（10分钟）

### 步骤5：保存并启动

1. **保存配置**
   - 点击"Save"按钮保存配置

2. **启动MCP服务器**
   - 在配置列表中找到刚创建的服务器
   - 点击"Start"按钮启动

---

## 方案二：手动JSON配置

### 步骤1：定位配置文件

#### VSCode用户配置
```bash
# Windows
%APPDATA%/Code/User/settings.json

# macOS
~/Library/Application Support/Code/User/settings.json

# Linux
~/.config/Code/User/settings.json
```

#### 项目级配置（推荐）
在项目根目录创建：`.vscode/mcp.json`

### 步骤2：编辑配置文件

#### 基础Web UI配置
```json
{
  "mcp": {
    "servers": {
      "mcp-feedback-enhanced": {
        "command": "uvx",
        "args": [
          "mcp-feedback-enhanced@latest"
        ],
        "timeout": 600,
        "env": {
          "MCP_WEB_HOST": "127.0.0.1",
          "MCP_WEB_PORT": "8765",
          "MCP_DEBUG": "false"
        },
        "autoApprove": ["interactive_feedback"]
      }
    }
  }
}
```

#### 桌面应用配置
```json
{
  "mcp": {
    "servers": {
      "mcp-feedback-enhanced": {
        "command": "uvx", 
        "args": [
          "mcp-feedback-enhanced@latest"
        ],
        "timeout": 600,
        "env": {
          "MCP_DESKTOP_MODE": "true",
          "MCP_WEB_PORT": "8765",
          "MCP_DEBUG": "false"
        },
        "autoApprove": ["interactive_feedback"]
      }
    }
  }
}
```

#### 完整高级配置
```json
{
  "mcp": {
    "servers": {
      "mcp-feedback-enhanced": {
        "command": "uvx",
        "args": [
          "mcp-feedback-enhanced@latest"
        ],
        "timeout": 600,
        "env": {
          "MCP_WEB_HOST": "127.0.0.1",
          "MCP_WEB_PORT": "8765",
          "MCP_DEBUG": "false",
          "MCP_LANGUAGE": "zh-CN",
          "MCP_AUTO_COMMIT": "true",
          "MCP_SESSION_TIMEOUT": "3600",
          "MCP_AUTO_SAVE": "true",
          "MCP_NOTIFICATION": "true"
        },
        "autoApprove": ["interactive_feedback"]
      }
    }
  }
}
```

### 步骤3：配置参数详解

#### 环境变量说明
```json
{
  "MCP_WEB_HOST": "127.0.0.1",      // Web界面绑定地址
  "MCP_WEB_PORT": "8765",           // Web界面端口
  "MCP_DEBUG": "false",             // 调试模式开关
  "MCP_LANGUAGE": "zh-CN",          // 界面语言
  "MCP_DESKTOP_MODE": "true",       // 桌面应用模式
  "MCP_AUTO_COMMIT": "true",        // 自动提交功能
  "MCP_SESSION_TIMEOUT": "3600",    // 会话超时时间(秒)
  "MCP_AUTO_SAVE": "true",          // 自动保存会话
  "MCP_NOTIFICATION": "true"        // 系统通知开关
}
```

#### 远程开发特殊配置
```json
{
  "mcp": {
    "servers": {
      "mcp-feedback-enhanced": {
        "command": "uvx",
        "args": [
          "mcp-feedback-enhanced@latest"
        ],
        "timeout": 600,
        "env": {
          "MCP_WEB_HOST": "0.0.0.0",  // 允许远程访问
          "MCP_WEB_PORT": "8765",
          "MCP_DEBUG": "false"
        },
        "autoApprove": ["interactive_feedback"]
      }
    }
  }
}
```

### 步骤4：重启和验证

1. **重启VSCode**
   - 完全关闭VSCode
   - 重新打开项目

2. **验证配置加载**
   - 打开Copilot Chat
   - 切换到Agent模式
   - 检查工具列表中是否出现mcp-feedback-enhanced

---

## 启动和测试验证

### 手动启动测试
```bash
# 测试基本功能
uvx mcp-feedback-enhanced@latest test

# 测试Web界面
uvx mcp-feedback-enhanced@latest test --web

# 测试桌面应用
uvx mcp-feedback-enhanced@latest test --desktop
```

### 验证Web界面访问
1. **启动服务后**，浏览器应自动打开：`http://127.0.0.1:8765`
2. **如果没有自动打开**，手动访问该地址
3. **验证功能**：
   - 界面是否正常显示
   - 是否支持中文界面
   - 文件上传功能是否正常

### 验证Copilot集成
1. **在Copilot Chat中**输入测试请求：
   ```
   请帮我收集一些用户反馈信息
   ```

2. **观察是否**：
   - 自动启动mcp-feedback-enhanced工具
   - 打开反馈收集界面
   - 能够正常交互

---

## 故障排除

### 常见问题解决

#### 1. 工具无法启动
```bash
# 检查uvx是否正常
uvx --version

# 清理缓存重试
uv cache clean
uvx mcp-feedback-enhanced@latest test
```

#### 2. 端口占用问题
```bash
# 检查端口占用
netstat -an | grep 8765  # Linux/macOS
netstat -an | findstr 8765  # Windows

# 更换端口配置
"MCP_WEB_PORT": "9000"
```

#### 3. 权限问题
```json
{
  "autoApprove": ["interactive_feedback", "*"]  // 临时允许所有工具
}
```

#### 4. WebSocket连接失败
- 刷新浏览器页面
- 检查防火墙设置
- 确认端口正确开放

### 日志查看
```bash
# 启用调试模式
"MCP_DEBUG": "true"

# 查看详细日志
uvx mcp-feedback-enhanced@latest test --debug
```

---

## 使用示例和场景

### 基本反馈收集
1. 在Copilot Chat中请求：
   ```
   我需要收集用户对这个新功能的反馈意见
   ```

2. 工具自动启动反馈界面

3. 用户可以：
   - 输入文字反馈
   - 上传截图
   - 选择评分等级

### 代码审查反馈
1. 请求Copilot：
   ```
   帮我创建一个代码审查反馈表单
   ```

2. 自动生成包含：
   - 代码质量评估
   - 改进建议收集
   - 问题标记功能

### 项目进度汇报
1. 使用自动命令执行功能：
   ```json
   "MCP_AUTO_COMMIT": "true"
   ```

2. 自动收集：
   - 开发进度信息
   - 遇到的问题
   - 下一步计划

这个配置指导涵盖了两种主要配置方案的详细步骤。你可以根据自己的使用场景选择合适的方案进行配置。 