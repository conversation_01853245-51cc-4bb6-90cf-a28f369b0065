# 方案文档：V2Ray/Xray “一劳永逸”方案

## 0. 需求与目标

**核心需求：** 在中国大陆地区，能够稳定、安全、高效地使用 Google 官方提供的 `gemini-cli` 命令行工具。

**主要挑战：**

1.  `gemini-cli` 在认证和API请求时，需要访问 `*.googleapis.com` 等域名，这些服务在中国大陆地区无法直接访问。
2.  需要一个长期稳定、操作简便的解决方案，避免因网络问题频繁中断工作流。

**本方案目标：**

- 建立一条从本地开发环境到海外服务器的安全、加密的数据通道。
- 实现对 `gemini-cli` 网络请求的智能代理，使其能无缝通过该通道访问 Google 服务。
- 达到“一次配置，日常无感”的最佳体验，不影响国内服务的正常访问。

---

## 1. 方案概述

本方案旨在通过在服务器上部署 `Xray` 核心并结合本地客户端，为 `gemini-cli` 提供一个性能更优、体验更无感的网络代理环境。与纯 SSH 隧道方案相比，它更适合追求极致稳定性和便利性的用户。

**核心优势：**

- **性能卓越**：采用 `VLESS` 等专为代理设计的协议，在复杂网络环境下的连接稳定性、速度和抗干扰能力通常优于 SSH 隧道。
- **无感体验**：通过本地客户端的智能路由（规则分流），可以实现“一次配置，永久生效”。访问 Google 相关服务时自动走代理，访问国内服务则直连，无需像 SSH 方案那样为每个终端手动设置代理，也无需频繁开关浏览器插件。
- **多设备支持**：一套服务器配置，可以轻松支持您的电脑、手机等多种设备同时连接使用。

**主要权衡：**

- **本地需安装软件**：此方案必须在本地电脑上安装一个客户端程序（如 `Clash Verge Rev`），这与 SSH 方案的“本地零安装”原则不同。

## 2. 技术栈

- **服务器**：腾讯云轻量应用服务器（地域：亚太，系统：Ubuntu 24.04 LTS）
- **核心代理软件**：`Xray-core` (V2Ray 的上游项目，功能更强大)
- **代理协议**：`VLESS + TCP + Vision` (一种当前主流、高性能且难以被识别的组合)
- **macOS 客户端**：`Clash Verge Rev` (一个主流、强大且持续更新的图形化客户端)

---

## 3. 服务器端部署流程

在开始部署核心服务前，我们首先对服务器进行一系列基础但至关重要的安全加固，建立“纵深防御”体系。

#### **步骤 3.0: 服务器安全加固**

##### **1. 配置 UFW 防火墙**

UFW (Uncomplicated Firewall) 是 Ubuntu 上简单易用的防火墙前端。我们将配置它为“默认拒绝，按需允许”的白名单模式。

```bash
# 1. 允许您自定义的 SSH 端口 (例如 22822)，如果未修改则为 22
sudo ufw allow 22822/tcp

# 2. 允许您计划用于 Xray 的服务端口 (例如 31234)
sudo ufw allow 31234/tcp

# 3. 启用 UFW 防火墙
# ！！！警告：执行此命令前，请务必确认已放行您的 SSH 端口，否则将立即失联！
sudo ufw enable

# 4. 检查防火墙状态，确认规则已生效
sudo ufw status verbose
```
您应该能看到您放行的两个端口状态为 `ALLOW IN`，其他所有流量默认为 `DENY IN`。

##### **2. 安装并配置 Fail2Ban**

Fail2Ban 能有效防止针对 SSH 的暴力破解攻击。默认情况下，它只监控标准的 22 端口，因此在我们修改了 SSH 端口后，必须手动告知 Fail2Ban 新的端口号。

```bash
# 1. 安装 Fail2Ban
sudo apt update && sudo apt install -y fail2ban

# 2. 创建本地配置文件，以防升级时被覆盖
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# 3. 编辑本地配置文件
sudo nano /etc/fail2ban/jail.local
```
在 nano 编辑器中，使用 `Ctrl+W` 搜索 `[sshd]`，并修改其下的 `port` 和 `enabled` 字段，如下所示：
```ini
[sshd]
enabled = true
port    = 22822  # 警告：这里必须填写您自定义的 SSH 端口
```
保存文件 (`Ctrl+X`, `Y`, `Enter`) 后，重启 Fail2Ban 服务使其加载新配置。
```bash
# 4. 重启并设置为开机自启
sudo systemctl restart fail2ban
sudo systemctl enable fail2ban
```
现在，Fail2Ban 已开始监控您自定义的 SSH 端口，并会自动封禁有恶意破解行为的 IP。

##### **3. 启用自动安全更新**

确保系统能自动安装最新的安全补丁，是防止漏洞攻击的最佳实践。

```bash
# 1. 安装无人值守更新工具包
sudo apt install -y unattended-upgrades

# 2. 启用服务
sudo dpkg-reconfigure -plow unattended-upgrades
```
执行后，系统会弹出一个紫色的文本界面，询问是否自动下载和安装稳定更新，选择 **`<Yes>`** 并回车即可。

##### **4. 配置并验证 SSH 密钥登录 (推荐)**
这是大幅提升服务器安全性的核心步骤。我们将从本地电脑生成一对密钥（公钥和私钥），然后将公钥上传到服务器。此后，您将能通过私钥“证明”自己的身份，无需输入密码即可登录。

**1. 在本地电脑生成密钥对**

如果您本地还没有 SSH 密钥，或者想为这台服务器生成一对专用的密钥，请打开您**本地 Mac 的终端**，执行以下命令：

```bash
# -t 指定加密算法为 ed25519 (当前最推荐)
# -C "gemini-proxy-server-key" 是一个注释，方便您识别密钥
ssh-keygen -t ed25519 -C "gemini-proxy-server-key"
```
执行后，系统会询问您几个问题：
- `Enter file in which to save the key...`: 按回车键接受默认路径 (`~/.ssh/id_ed25519`)。如果您想使用自定义名称（例如 `~/.ssh/gemini_key`），请在此处输入完整路径。
- `Enter passphrase (empty for no passphrase):`: 强烈建议设置一个密码！这个密码用来保护您的私钥文件，即使私钥文件泄露，没有密码也无法使用。请设置一个强密码并牢记。

命令执行成功后，会在 `~/.ssh/` 目录下生成两个文件：
- `id_ed25519`: 您的**私钥**，必须妥善保管，绝不能泄露。
- `id_ed25519.pub`: 您的**公钥**，可以安全地分享和上传到任何您想访问的服务器。

**2. 上传公钥到服务器**

接下来，我们需要将本地的公钥内容添加到服务器的 `authorized_keys` 文件中。`ssh-copy-id` 命令可以帮我们自动完成这个过程。

请在**本地终端**执行：

```bash
# 确保将 myuser、服务器IP、端口号 替换为您自己的信息
# 如果您上一步生成了自定义名称的密钥，请用 -i 参数指定公钥路径
# 例如: ssh-copy-id -i ~/.ssh/gemini_key.pub ...
ssh-copy-id -p 22822 myuser@[您的服务器公网IP]
```
命令会提示您输入服务器的登录密码。输入正确后，您的公钥就会被自动追加到服务器上 `~/.ssh/authorized_keys` 文件的末尾。

**3. 测试密钥登录**

现在，您可以尝试用密钥来登录服务器了。在**本地终端**执行：

```bash
# 如果您使用了自定义名称的密钥，请用 -i 参数指定私钥路径
# 例如: ssh -i ~/.ssh/gemini_key ...
ssh -p 22822 myuser@[您的服务器公网IP]
```
如果一切顺利，系统会提示您输入之前为密钥设置的**passphrase**（而不是服务器的登录密码）。输入正确后，您应该能成功登录服务器。这证明您的密钥已经配置成功！

##### **5. 禁用密码登录 (终极安全)**
这是安全加固的最后一步，也是最关键的一步。在成功配置并验证了密钥登录后，我们就可以彻底关闭传统的密码登录方式，只允许通过密钥访问服务器，杜绝一切密码被暴力破解的风险。

**！！！高危警告！！！**
在执行此操作前，请**务必**按照上一步（`4. 配置并验证 SSH 密钥登录`）的指引，**确认您已经可以通过 SSH 密钥成功登录服务器**。

如果您跳过了上一步，或者对密钥登录没有百分之百的把握，**请不要执行以下操作**。否则，一旦出错，**将导致您永久失去对服务器的访问权限！**

```bash
# 1. 编辑 SSH 服务配置文件
sudo nano /etc/ssh/sshd_config

# 2. 修改配置项
# 在 nano 编辑器中，使用 Ctrl+W 搜索以下关键字，并确保它们的值如下所示。
# 如果某行以 # 开头，请删掉行首的 # 使其生效。

PubkeyAuthentication yes
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM no # 建议设为 no，在某些配置下 UsePAM yes 可能会绕过 PasswordAuthentication no 的限制

# 3. 保存并退出
# 按 Ctrl+X, Y, Enter 保存文件。

# 4. 重启 SSH 服务以应用新配置
sudo systemctl restart sshd
```

###### **配置项详解**
- **`PasswordAuthentication no`**: 这是最核心的指令，直接禁用了标准的密码认证方式。
- **`ChallengeResponseAuthentication no`**: 这禁用了另一种较旧的、交互式的“挑战-应答”认证模式。一些系统配置下，它可能被利用来绕过 `PasswordAuthentication no` 的限制，因此明确禁用它可以堵上这个潜在的后门。
- **`UsePAM no`**: PAM (Pluggable Authentication Modules) 是一个非常灵活的认证框架，但它也可能引入复杂的认证链，导致绕过我们预期的“仅密钥”策略。将其禁用可以强制 SSH 只使用它自身内置的、更简单和可控的认证方法（如公钥认证），确保策略的严格执行。

通过将这三个选项全部设置为 `no`，我们构建了多层防御，彻底杜绝了任何通过密码登录的可能性，确保了服务器的最高安全级别。

#### **步骤 3.1: 准备工作**

1.  **购买服务器**：参考 SSH 方案，购买一台符合配置的腾讯云轻量应用服务器。
2.  **防火墙配置**：
    - 在腾讯云控制台的防火墙页面，**放行（允许）** 一个您自定义的高位端口，例如 `31234`。此端口将用于 Xray 服务的监听。**请务必记下这个端口号**。
    - SSH 方案中强化的 `22822` 端口可以保留，用于服务器管理。

#### **步骤 3.2: 一键安装 Xray**

1.  **登录服务器**：使用 SSH 登录您的服务器。
    ```bash
    ssh myuser@[您的服务器公网IP] -p 22822
    ```
2.  **执行官方安装脚本**：使用 `root` 权限运行 `Xray-core` 官方提供的一键安装脚本。这个脚本会自动处理安装、注册为系统服务等所有事宜。
    ```bash
    sudo bash -c "$(curl -L https://github.com/XTLS/Xray-install/raw/main/install-release.sh)" @ install
    ```
    看到 `Xray is installed.` 的提示即表示安装成功。

#### **步骤 3.3: 生成并配置 Xray**

1.  **生成 UUID**：Xray 使用 UUID 作为用户的唯一身份凭证。执行以下命令生成一个：
    ```bash
    xray uuid
    ```
    **复制并妥善保管这个生成的 UUID**，它等同于您的密码。

2.  **生成 Reality 密钥对 (关键步骤)**：Reality 协议需要一对公钥和私钥来进行认证。
    ```bash
    xray x25519
    ```
    执行后会输出两行内容：
    `Private key:` (私钥)
    `Public key:` (公钥)
    **请务必将这两个密钥都复制并妥善保管好**。私钥用于服务器，公钥用于客户端。

3.  **编辑配置文件**：Xray 的主配置文件位于 `/usr/local/etc/xray/config.json`。
    ```bash
    sudo nano /usr/local/etc/xray/config.json
    ```

4.  **粘贴配置模板**：**清空** `config.json` 文件中的所有默认内容，然后将下面的配置模板**完整地**粘贴进去。

    ```json
    {
      "log": {
        "loglevel": "warning"
      },
      "inbounds": [
        {
          "port": 31234, // 警告：改成您在防火墙放行的那个端口
          "protocol": "vless",
          "settings": {
            "clients": [
              {
                "id": "在这里粘贴您生成的UUID", // 警告：改成您生成的UUID
                "flow": "xtls-rprx-vision"
              }
            ],
            "decryption": "none"
          },
          "streamSettings": {
            "network": "tcp",
            "security": "reality",
            "realitySettings": {
              "show": false,
              "dest": "www.bing.com:443",
              "xver": 0,
              "serverNames": [
                "www.bing.com"
              ],
              "privateKey": "在这里粘贴您生成的Private key", // 警告：改成您生成的私钥
              "shortIds": [
                ""
              ]
            }
          }
        }
      ],
      "outbounds": [
        {
          "protocol": "freedom",
          "tag": "direct"
        }
      ]
    }
    ```

5.  **修改关键参数**：
    - 将 `port` 字段的值修改为您自己的**防火墙放行端口**。
    - 将 `id` 字段的值修改为您自己生成的 **UUID**。
    - 将 `privateKey` 字段的值修改为您上一步生成的**私钥 (Private key)**。

6.  **保存并重启 Xray**：
    - 按 `Ctrl+X`, `Y`, `Enter` 保存文件。
    - 执行以下命令重启 Xray 服务以加载新配置。
    ```bash
    sudo systemctl restart xray
    ```
7.  **检查服务状态**：确认 Xray 服务是否正在正常运行。
    ```bash
    sudo systemctl status xray
    ```
    看到 `active (running)` 的绿色字样即表示服务器端配置成功。

---

## 4. 本地客户端配置 (Clash Verge Rev)

本方案采用 `Clash Verge Rev` 作为本地客户端，它功能强大、社区活跃，能完美支持我们服务器的 `VLESS + Reality` 协议。

#### **步骤 4.1: 通过 Homebrew 安装**

使用 Homebrew 可以非常方便地安装和管理 `Clash Verge Rev`。在您的本地 Mac 终端中运行：
```bash
brew install --cask clash-verge-rev
```
安装完成后，您可以在“应用程序”文件夹中找到它。

#### **步骤 4.2: 准备 Clash 配置文件**

Clash 使用 `YAML` 格式的配置文件。请在您的电脑上创建一个名为 `gemini-proxy.yaml` 的新文件，并将以下内容**完整地**粘贴进去。

```yaml
# gemini-proxy.yaml
# 本地代理端口配置
mixed-port: 7897
socks-port: 7890 # 我们将主要使用这个 SOCKS5 代理端口
allow-lan: false
mode: rule
log-level: info

# 代理服务器配置
proxies:
  - name: "gemini-reality-proxy"
    type: vless
    server: "服务器公网IP"    # 警告: 填入您的服务器公网IP
    port: 31234              # 警告: 填入您在服务器上配置的端口
    uuid: "您的UUID"            # 警告: 填入您生成的UUID
    network: tcp
    tls: true
    udp: true
    flow: xtls-rprx-vision
    client-fingerprint: chrome
    servername: www.bing.com # 必须与服务器 realitySettings.dest 的域名一致
    reality-opts:
      public-key: "您的公钥"  # 警告: 填入您生成的公钥 (Public key)
      short-id: ""           # shortId, 服务器端配置了空值，这里也留空

# 代理组配置
proxy-groups:
  - name: "GEMINI-GROUP"
    type: select
    proxies:
      - "gemini-reality-proxy"

# 规则配置 (简单匹配所有流量到代理组)
rules:
  - MATCH,GEMINI-GROUP
```

**在粘贴后，请务必将 `server`、`port`、`uuid` 和 `public-key` 四个字段的值，修改为您自己的服务器信息。**

#### **步骤 4.3: 在 Clash Verge 中导入并激活配置**

1.  启动 `Clash Verge Rev`。
2.  在左侧菜单栏选择 **“Profiles” (配置)**。
3.  将您刚刚创建并修改好的 `gemini-proxy.yaml` 文件，直接拖拽到 Clash Verge 的窗口中。
4.  导入成功后，您会看到一个新的配置文件条目。点击它，使其处于选中状态（前方有一个绿色的竖条），即表示该配置已激活。

---

## 5. 日常使用工作流 (精准代理模式)

此工作流的核心是：让 Clash Verge 在后台保持与服务器的连接，但**不接管系统网络**，仅在我们需要时，**手动指定** `gemini-cli` 去使用它。

#### **步骤 5.1: 启动客户端并确认模式**

1.  启动 `Clash Verge Rev`，并确保您在“Profiles”中已经激活了 `gemini-proxy` 配置。
2.  回到主界面 **“Dashboard” (仪表盘)**。
3.  **【最关键的一步】** 找到界面中的 **“System Proxy” (系统代理)** 开关，**确保它是关闭的！** 关闭状态下，开关是灰色的。这意味着 Clash Verge 不会影响您的浏览器、微信等任何其他程序的网络。

#### **步骤 5.2: 为 `gemini-cli` 精准开启代理**

1.  在 Clash Verge 左侧菜单栏选择 **“Settings” (设置)**。
2.  在右侧的设置项中，找到 **“Network” (网络)** 部分，记下 **“SOCKS Port”** 的端口号，默认为 `7890`。
3.  **打开一个全新的终端窗口/标签页**。
4.  当您需要运行 `gemini-cli` 时，使用以下格式的命令：

    ```bash
    ALL_PROXY=socks5://127.0.0.1:7890 gemini "你的问题"
    ```
    (请将 `7890` 替换为您在上一步看到的实际端口号)。

#### **工作原理解析**

-   `ALL_PROXY=...` 是一个**临时环境变量**，它只对紧跟其后的 `gemini` 命令生效。
-   当 `gemini` 命令执行完毕后，这个环境变量会自动失效。
-   这种方式实现了最精准的控制：只有您想代理的命令才会走代理，而系统的其他部分完全不受影响。这完美达成了我们“仅为 `gemini-cli` 开启代理”的目标。

---

## 6. 进阶工作流：智能路由模式 (一劳永逸)

“精准代理模式”虽然精确，但操作略显繁琐。本章节将介绍如何配置 Clash 的核心功能——**规则模式**和**系统代理**，实现“一次配置，永久无感”的最终体验。

**目标：** 打开一个总开关后，无论是浏览器还是终端，访问 Google 等服务时自动走代理，访问国内服务时自动直连，无需任何手动切换。

#### **步骤 6.1: 准备一份带规则的配置文件**

智能路由的核心是一份包含规则的配置文件。请在您的电脑上创建一个新文件，命名为 `gemini-proxy-rules.yaml`，并将以下内容完整粘贴进去。

```yaml
# gemini-proxy-rules.yaml
# 包含智能分流规则的 Clash 配置文件

# 本地代理端口配置 (与之前保持一致)
mixed-port: 7897
socks-port: 7890
allow-lan: false
mode: rule # 模式设置为“规则模式”
log-level: info

# 代理服务器配置 (与之前完全一样，直接复制即可)
proxies:
  - name: "gemini-reality-proxy"
    type: vless
    server: "服务器公网IP"    # 警告: 填入您的服务器公网IP
    port: 31234              # 警告: 填入您在服务器上配置的端口
    uuid: "您的UUID"            # 警告: 填入您生成的UUID
    network: tcp
    tls: true
    udp: true
    flow: xtls-rprx-vision
    client-fingerprint: chrome
    servername: www.bing.com # 必须与服务器 realitySettings.dest 的域名一致
    reality-opts:
      public-key: "您的公钥"  # 警告: 填入您生成的公钥 (Public key)
      short-id: ""

# 代理组配置 (与之前完全一样)
proxy-groups:
  - name: "GEMINI-PROXY"
    type: select
    proxies:
      - "gemini-reality-proxy"
  - name: "CN-DIRECT" # 新增一个名为“直连”的策略组
    type: select
    proxies:
      - DIRECT

# 规则配置 (这是实现智能分流的核心)
# 规则的匹配顺序是从上到下，一旦匹配成功，后续规则不再执行。
rules:
  # 规则一：精确捕获 Gemini CLI 的 API 请求 (最高优先级)
  - DOMAIN-SUFFIX,generativelanaguage.googleapis.com,GEMINI-PROXY

  # 规则二：捕获其他 Google 及 GitHub 服务
  - DOMAIN-KEYWORD,google,GEMINI-PROXY
  - DOMAIN-SUFFIX,github.com,GEMINI-PROXY
  
  # 规则三：所有中国大陆地区的 IP 地址，走直连
  - GEOIP,CN,CN-DIRECT

  # 规则四：所有其他未匹配到的流量，全部走代理
  # 这是“兜底”规则，确保默认行为是走代理
  - MATCH,GEMINI-PROXY
```
**请注意：**
- 和之前一样，您需要将 `proxies` 部分的服务器信息替换为您自己的。
- **为何能保证 `gemini-cli` 走代理？** 我们在规则列表的顶部增加了一条 `DOMAIN-SUFFIX,generativelanguage.googleapis.com,GEMINI-PROXY`。`gemini-cli` 进行 API 调用时，访问的正是这个核心域名。Clash 会根据这条最高优先级的规则，精确地将其网络请求匹配到 `GEMINI-PROXY` 代理策略，从而确保其流量被正确代理。
- 这是一份非常基础的规则集，您可以根据需要自行添加更多规则（例如 `DOMAIN-SUFFIX,twitter.com,GEMINI-PROXY`）。

#### **步骤 6.2: 导入规则并开启智能路由**

**1. 导入并激活规则配置文件:**
   a. 在您的本地电脑上，打开 `Clash Verge Rev` 应用程序。
   b. 点击左侧菜单栏的 **“订阅 (Profiles)”** 图标，进入配置文件管理页面。
   c. 将您之前创建好的 `gemini-proxy-rules.yaml` 文件，用鼠标**从文件夹直接拖拽到 Clash Verge 的窗口中**。
   
> **其他导入方法**:
> - **复制粘贴法**: 您也可以用文本编辑器打开 `.yaml` 文件，复制其全部内容。然后在 Clash Verge 的“订阅”页面点击“新建(New)”，将内容粘贴进去并保存。
> - **本地服务器法**: 在文件所在目录用终端开启一个 `python3 -m http.server 8000` 服务，然后在 Clash Verge 中输入 `http://127.0.0.1:8000/gemini-proxy-rules.yaml` 进行订阅。

   d. 导入成功后，在列表中**单击**这个新的 `gemini-proxy-rules` 条目，确保其前方出现一个**蓝色的竖条**。这代表新规则已激活。

**2. 开启系统代理:**
   a. 点击左侧菜单栏的 **“首页 (Dashboard)”** 图标，回到主界面。
   b. **【最关键的一步】** 在“网络设置”卡片中，找到 **“系统代理 (System Proxy)”** 这个开关，点击它。
   c. **确保开关是开启的** (开关会变为彩色)。

**3. 验证智能路由效果:**
   a. 打开您的浏览器（无需任何代理插件）。
   b. 尝试访问 `google.com`，应该能正常打开。
   c. 尝试访问 `baidu.com`，应该也能流畅打开，没有延迟感。
   d. 如果两者都符合预期，证明您的智能路由已配置成功。您不再需要 SwitchyOmega 等浏览器插件。

#### **步骤 6.3: (推荐) 永久配置终端代理**

**重要前提**：终端环境在 macOS/Linux 中默认独立于图形界面的“系统代理”。因此，若想让 `gemini-cli` 等命令行工具也享受智能路由，我们**必须**为其配置代理环境变量。将其写入 Shell 配置文件是实现“一劳永逸”的最佳方法。

1.  **编辑 Shell 配置文件**：
    - 如果您使用 Zsh (macOS 默认)，请执行 `nano ~/.zshrc`。
    - 如果您使用 Bash，请执行 `nano ~/.bash_profile`。

2.  **添加代理配置**：
    在文件的**末尾**，添加以下这一行：
    ```bash
    # 让终端默认使用 Clash 提供的 SOCKS5 代理
    export ALL_PROXY=socks5://127.0.0.1:7890
    ```
    (请将 `7890` 替换为您 Clash 设置中的实际 SOCKS 端口号)。

3.  按 `Ctrl+X`, `Y`, `Enter` 保存文件。

4.  **让配置立即生效**：执行 `source ~/.zshrc` (或 `source ~/.bash_profile`)。

**完成！** 从现在起，只要您本地的 Clash Verge Rev 正在运行且系统代理已开启，您就可以在**任何新打开的终端窗口**中，直接运行 `gemini "你的问题"`，它会自动通过代理进行连接，无需任何额外的前缀。

**重要权衡：**
- **优点**：极致的便利，完全的“一劳永逸”。
- **缺点**：永久的终端代理可能会与某些必须直连的国内命令行工具（例如使用了国内镜像源的 `npm`、`pip`，或部分云厂商的 CLI）产生冲突。
- **解决方案**：如果遇到冲突，您可以在运行这些特定命令时，临时取消代理：`ALL_PROXY="" a_conflicting_command`。

---

## 7. 安全性分析

本章节将阐述为何此方案能充分保障您的连接安全与用户唯一性。

#### **1. 连接的唯一性**

一个恶意的第三方想要连接您的代理服务，理论上必须同时具备以下所有信息：

-   您的 **服务器公网 IP 地址**。
-   您为 Xray 服务配置的 **端口号** (例如 `31234`)。
-   您在服务器上生成的独一无二的 **UUID**。
-   您在服务器上生成的 Reality **公钥**。
-   您在服务器上配置的伪装域名 **SNI** (例如 `www.bing.com`)。

缺少其中任何一环，都无法通过服务器的认证。特别是 `UUID` 和 `公钥` 这两个核心凭证，随机性极高，无法被猜解。

#### **2. 连接的安全性与抗探测性**

-   **端到端加密**：您本地的 `Clash Verge Rev` 到服务器的 `Xray` 之间的所有通信，都由 `VLESS` 和 `Reality` 协议进行了高强度加密。
-   **流量伪装**：`Reality` 协议最强大的地方在于，它使得您的代理流量在外界看来，与一个正常的、访问 `www.bing.com` 等网站的 TLS 1.3 加密流量**几乎没有区别**。它没有传统代理协议的“握手特征”，从而可以有效抵抗来自网络中间设备的主动探测和流量识别。
-   **防御暴力破解**：我们部署的 `Fail2Ban` 服务，专门用于防御针对 SSH 管理端口的暴力密码猜测攻击，进一步保护了服务器自身的安全。
-   **最小攻击面**：我们配置的 `UFW` 防火墙，将服务器所有非必要的端口都对外界关闭，只开放了维持服务所必需的 SSH 和 Xray 端口，极大地减少了潜在的攻击入口。

综上所述，本方案从**服务认证、流量加密、服务器自身防护**三个维度构建了一套完整的安全体系，足以确保您的连接是私密、安全且唯一的。

---

## 7. 附录

本附录包含在配置服务器和SSH密钥过程中常见的一些问题解答，以及备用的操作方法。

### 7.1 SSH 密钥常见问题 (FAQ)

#### Q1: `ed25519` 是什么意思？

`ed25519` 是一种现代、高效且安全的**加密算法**，用于生成您的 SSH 密钥对。

相比于传统的 RSA 算法，它拥有更高的安全性和更好的性能（连接更快），同时生成的密钥也更短，是当前 OpenSSH 官方最为推荐的密钥类型。我们所有文档中都优先使用 `ssh-keygen -t ed25519` 来创建密钥。

#### Q2: 为什么我已经用了密钥，登录时还要输入“密钥的密码” (Passphrase)？

这是一个非常关键的安全设计，涉及到两层保护：

1.  **第一层保护（保护服务器）：** 使用 SSH 密钥，是为了向服务器证明您的身份，从而**替代了输入服务器的登录密码**。
2.  **第二层保护（保护您的私钥）：** 输入**密钥的密码 (Passphrase)**，是为了向您**本地的电脑**证明您有权使用这个私钥文件。它用一个您自设的密码将您的私钥文件 (`id_ed25519`) 加密。

**结论：** 即使您的电脑被黑客入侵，私钥文件被盗，但由于它被您的密码加密着，黑客也无法立刻使用它来登录您的服务器。这为您赢得了宝贵的补救时间。这是一个用“轻微的不便”换取“极高安全性”的必要措施。

#### Q3: 如果忘记了密钥的密码 (Passphrase) 怎么办？

这个密码无法被找回。您唯一的办法就是**弃用这个密钥对，生成一个全新的**。

您需要通过其他方式（例如服务器的登录密码、备用密钥、或云控制台VNC）登录到服务器，然后从 `~/.ssh/authorized_keys` 文件中**删除旧的公钥**，并**添加新生成的公钥**。

**警告：** 如果您既忘了密钥密码，又没有任何其他方式可以登录服务器，您将永久失去对服务器的访问权限，唯一的办法就是通过云服务商重装系统（数据会丢失）。

#### Q4: 复制公钥时，需要包含末尾的邮箱地址吗？

**需要。** 公钥文件 (`id_ed25519.pub`) 的内容通常由三部分组成：`[算法类型] [密钥数据] [注释/邮箱]`。

您必须从行首的 `ssh-ed25519` 开始，一直到行尾的邮箱地址，将这一**整行**完整地复制下来。这才是一条标准、格式正确的公钥记录。

#### Q5: 如果忘记了 Xray 的 UUID 和 Reality 密钥怎么办？

这些凭证无法被直接找回，但因为您拥有服务器的最高管理权限，所以可以轻松地进行**重置**。

1.  **登录服务器后台**：通过 SSH 登录到您的服务器。
2.  **生成新凭证**：
    - 重新运行 `xray uuid` 生成一个新的 UUID。
    - 重新运行 `xray x25519` 生成一对新的 Reality 公私钥。
3.  **更新服务器配置**：
    - 打开 `/usr/local/etc/xray/config.json` 文件。
    - 将旧的 `id` (UUID) 和 `privateKey` 替换为新生成的值。
    - 保存并重启 Xray 服务 (`sudo systemctl restart xray`)。
4.  **更新本地客户端配置**：
    - 打开您本地的 Clash 配置文件 (`.yaml`)。
    - 将旧的 `uuid` 和 `public-key` 替换为新生成的值。
    - 在 Clash 客户端中重新加载或更新配置。

### 7.2 备用方法：通过云控制台上传SSH公钥

在某些特殊情况下，例如本地网络限制无法使用 `ssh-copy-id`，或者在修复服务器时需要添加备用密钥，您可以通过云服务商提供的网页控制台（VNC）来手动上传公钥。

本流程完全绕过远程SSH，直接在服务器上进行操作。

#### 第1步：在本地电脑获取公钥

1.  打开您的**本地电脑**终端。
2.  执行 `cat ~/.ssh/id_ed25519.pub` 或 `cat ~/.ssh/id_rsa.pub` 命令。
3.  **完整地复制**输出的那一长串以 `ssh-ed25519` 或 `ssh-rsa` 开头的公钥内容。

#### 第2步：通过腾讯云VNC控制台粘贴公钥

1.  登录腾讯云，进入轻量应用服务器的“VNC登录”或“OrcaTerm登录”网页终端。
2.  使用您的**用户名和密码**登录服务器。**请确保登录的是您希望配置密钥的普通用户**，而不是 `root`。
3.  登录后，依次执行以下命令：

    a. **创建 `.ssh` 目录并设置权限（如果不存在）：**
       ```bash
       mkdir -p ~/.ssh && chmod 700 ~/.ssh
       ```

    b. **将复制的公钥追加到授权文件中：**
       推荐使用 `echo` 命令，以避免手动编辑文件可能产生的错误。
       ```bash
       echo "在此处粘贴您本地复制的完整公钥" >> ~/.ssh/authorized_keys
       ```

    c. **为授权文件设置严格权限（关键步骤）：**
       ```bash
       chmod 600 ~/.ssh/authorized_keys
       ```
       如果权限不正确，SSH 服务将拒绝使用此文件。

#### 第3步：验证

操作完成后，打开一个全新的本地终端窗口，尝试通过 SSH 密钥直接登录。如果成功，则证明配置无误。 