# Clash Verge 使用指南

本指南旨在为用户提供一份详尽、具体且具有实践指导意义的 `Clash Verge Rev` 使用教程。我们将结合“任务导向”与“功能导向”的模式，从零开始，逐步深入，并最终解决常见问题。

---

## 引言：什么是 Clash Verge？

简单来说，`Clash Verge` 是一个图形化的网络代理客户端。

它的核心价值**不是**提供代理服务本身，而是作为一个智能的**交通调度中心**。它能根据您预设的**规则 (Rules)**，自动判断所有网络请求（如访问网站、App联网等）的去向：哪些应该通过代理服务器（走隧道），哪些应该直接连接（走本地网络）。

这种“智能分流”机制，使得我们可以在访问 `Google`、`GitHub` 等网站时自动使用代理，而在访问 `Bilibili`、`淘宝` 等国内服务时自动直连，从而实现无缝、无感的网络体验。

本教程以目前社区主流维护的 `Clash Verge Rev` 版本为例。

---

## 第一章：基础篇 - 从零到一的连接（任务）

### 任务目标
成功通过 `Clash Verge` 连接到您的代理服务器，并能通过它访问外部网络。

### 涉及功能模块与步骤

#### 1. 核心概念：配置文件 (Profile)

`Clash` 体系的灵魂是**配置文件**。它是一个 `.yaml` 格式的文本文件，里面定义了您所有的代理服务器信息、分流规则、策略组等一切行为准则。

获取配置文件通常有两种方式：
- **URL 订阅**：服务商提供的一个链接，`Clash Verge` 可以通过它定期自动更新服务器列表和规则。**这是最推荐的方式。**
- **本地文件**：一个静态的 `.yaml` 文件，需要手动更新。

#### 2. 导入您的配置文件

- 打开 `Clash Verge`，点击左侧菜单栏的 **“配置 (Profiles)”**。
- 在页面顶部的输入框中，粘贴您的 **URL 订阅链接**。
- 点击 **“导入 (Import)”**。

导入成功后，列表中会出现一个新的配置文件条目。

#### 3. 激活配置并选择节点

- 在“配置”页面，**点击**您刚刚导入的那个配置文件，使其前方出现一个**蓝色的竖条**。这代表该配置已被激活。
- 点击左侧菜单栏的 **“代理 (Proxies)”**。
- 在这里，您会看到配置文件作者预设的**策略组 (Proxy Groups)**，例如“国外流量”、“自动选择”、“香港节点”等。
- 找到一个允许手动选择的策略组（通常是 `select` 类型），在其中**选择一个您想使用的代理服务器节点**。

#### 4. 开启系统代理

- 点击左侧菜单栏的 **“仪表盘 (Dashboard)”**。
- 找到界面中央的 **“系统代理 (System Proxy)”** 开关，点击开启它。
- 开关变为**彩色**（通常是绿色或蓝色），即表示 `Clash Verge` 已经开始接管您电脑的系统网络。

#### 5. 验证

打开浏览器，尝试访问 `google.com`。如果能正常打开，恭喜您，基础连接已成功建立！

---

## 第二章：进阶篇 - 智能分流的奥秘（任务）

### 任务目标
理解并利用 `Clash` 的核心功能——规则模式，实现国内网站直连、国外网站自动走代理的“无感”体验。

### 涉及功能模块与概念

#### 1. 模式 (Mode) 的选择

在“代理 (Proxies)”页面的顶部，您可以看到 `Clash` 的三种核心工作模式：

- **全局 (Global)**：所有网络流量都**强制**通过您在“GLOBAL”策略组中选择的那个代理节点。适合临时需要全局代理的场景。
- **直连 (Direct)**：所有网络流量都**强制**不走代理，直接连接。
- **规则 (Rule)**：**最核心、最强大的模式。** `Clash` 会根据配置文件的规则列表，自动判断每个网络请求的走向。这是实现智能分流的基础。

**日常使用时，请务必保持在“规则 (Rule)”模式。**

#### 2. 解读规则 (Rules)

- 点击左侧菜单栏的 **“规则 (Rules)”**。
- 这里展示了当前配置文件生效的所有规则。`Clash` 会**从上到下**依次匹配这些规则。一旦某个网络请求命中了某条规则，就不再继续往下匹配。

一条典型的规则形如：`DOMAIN-SUFFIX, google.com, PROXY`
- `DOMAIN-SUFFIX`: 规则类型，表示匹配域名后缀。
- `google.com`: 匹配的内容。
- `PROXY`: 匹配成功后采取的策略（此处的 `PROXY` 是一个策略组的名称）。

这条规则的含义是：“所有以 `.google.com` 结尾的域名，都应执行 `PROXY` 策略组的方案（即走代理）”。

常见的兜底规则：
- `GEOIP, CN, DIRECT`: 匹配目标 IP 地址归属地为中国大陆的，走 `DIRECT` (直连) 策略组。
- `MATCH, PROXY`: 以上所有规则都未匹配到的流量，执行 `PROXY` 策略组的方案。这确保了未知流量默认走代理。

---

## 第三章：常见问题与排错指南 (FAQ)

#### Q1: 为什么开启了系统代理，还是上不了网？
1.  **检查订阅**：在“配置”页，尝试手动更新一下订阅，看是否能成功获取节点。
2.  **检查节点**：在“代理”页，确保您选择的不是一个超时的节点（延迟显示 `timeout`)。尝试切换到其他节点。
3.  **检查模式**：确保您处于“规则”或“全局”模式，而不是“直连”模式。
4.  **查看日志**：在“日志 (Logs)”页，查看是否有红色的错误信息。

#### Q2: 某个网站打不开或加载异常，如何处理？
1.  **分析连接**：打开“连接”页面，在上方搜索框输入该网站的域名。
2.  **定位问题**：查看它命中了什么规则 (`Rule`)，走了哪个节点 (`Proxy`)。
    - 如果显示 `DIRECT`，说明它被规则错误地识别为国内流量。
    - 如果走了一个很慢的节点，可以手动切换。
3.  **添加自定义规则**：
    - 在“规则”页，点击右上角的“编辑规则”。
    - 在`prepend-rules:`下添加一条新规则，例如 `DOMAIN-SUFFIX, a-blocked-site.com, PROXY`，将其强制指向代理策略组。

#### Q3: 如何让终端里的 `gemini-cli`, `git`, `curl` 也走代理？
“系统代理”开关通常不影响终端环境。您需要手动为终端设置代理环境变量。

**临时生效（仅对当前终端窗口有效）：**
```bash
export ALL_PROXY=socks5://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
```
> **注意**：端口号需与 `Clash Verge` “设置 (Settings)” -> “Network” 中显示的 `SOCKS Port` (7890) 和 `Mixed Port` (7897) 保持一致。

**永久生效（推荐）：**
将上述 `export` 命令添加到您的 Shell 配置文件中。
- 如果您使用 Zsh (macOS 默认)，执行 `nano ~/.zshrc`。
- 如果您使用 Bash，执行 `nano ~/.bash_profile`。
将命令粘贴到文件末尾，保存后执行 `source ~/.zshrc` (或 `source ~/.bash_profile`) 使其生效。

#### Q4: 设置终端代理后，`npm` 或 `pip` 访问国内镜像源变慢怎么办？
这是因为它们也被代理了。在执行这些特定命令时，可以临时取消代理：
```bash
ALL_PROXY="" https_proxy="" http_proxy="" npm install
```

#### Q5: 如何更新订阅的服务器节点？
在“配置”页面，找到您的订阅项目，点击其右侧的**“更新 (Update)”**图标即可。

#### Q6: `Clash Verge` 和 `Clash Verge Rev` 有什么区别？
`Clash Verge` 原项目已停止更新。`Clash Verge Rev` 是由社区接手维护的活跃分支，使用了更新、更强大的 `Mihomo (Clash.Meta)` 开源内核，功能更多，性能更好。**推荐始终使用 `Clash Verge Rev`**。

#### Q7: 如何为一个新的网站（如 Twitter）添加代理规则？
这是一个非常常见的需求。当您发现某个网站无法访问，并希望它也走代理时，可以按以下步骤操作。

**第一步：分析原因（可选但推荐）**
1.  打开 Clash Verge 的“连接 (Connections)”页面。
2.  在搜索框中输入该网站的域名（例如 `twitter.com`）。
3.  查看它命中了什么规则 (`Rule`)。如果显示 `DIRECT` 或命中了 `GEOIP,CN` 规则，就说明它被错误地直连了。

**第二步：编辑配置文件**
1.  用任何文本编辑器打开您当前使用的那个包含规则的 `.yaml` 配置文件（例如 `gemini-proxy-rules.yaml`）。
2.  在文件中找到 `rules:` 列表。

**第三步：添加新规则到正确位置**
将新规则插入到**其他具体代理规则之后**，但在**通用规则（如 `GEOIP,CN,DIRECT`）之前**。

```diff
 rules:
   # ... 其他已有的具体代理规则 ...
   - DOMAIN-SUFFIX,github.com,GEMINI-PROXY
  # 新增的规则：让 Twitter 走代理
  - DOMAIN-SUFFIX,twitter.com,GEMINI-PROXY
   
  # --- 通用规则开始，新规则要放在这之上 ---
  - GEOIP,CN,CN-DIRECT
  - MATCH,GEMINI-PROXY
```
> **提示**: `DOMAIN-SUFFIX` 是最常用的规则类型，它能匹配一个网站及其所有子域名，是添加网站规则的首选。

**第四步：保存并重载配置**
1.  保存您对 `.yaml` 文件的修改。
2.  回到 Clash Verge 客户端，在左侧的 **“Profiles” (配置)** 页面。
3.  找到您正在使用的配置文件，点击其右侧的**重载(Reload)**图标（一个环形箭头）。

操作完成。您的新规则已立即生效。

#### Q8: 如何让我的手机、游戏机（PS5, Switch）等设备也通过电脑的Clash上网？
这是一个非常实用的功能，能让您局域网内的所有设备都享受到电脑上配置好的代理服务。其原理是把您的电脑变成一个“代理网关”。

**前提条件：**
- 您的电脑和需要代理的设备（如手机）必须连接在**同一个Wi-Fi网络**（同一个局域网）下。

**第一步：在电脑上操作 (`Clash Verge`)**

1.  **开启局域网共享功能**
    -   打开 `Clash Verge` -> 进入“设置 (Settings)” -> “常规 (General)”。
    -   找到并**开启“允许局域网连接 (Allow LAN)”** 这个开关。

2.  **获取两个关键信息**
    -   **端口号**：在“设置 (Settings)” -> “网络 (Network)”页面，记下 **“混合端口 (Mixed Port)”** 的端口号（默认为 `7897` 或 `7890`）。
    -   **电脑的IP地址**：
        -   在 macOS 上：打开“系统设置” -> “网络” -> “Wi-Fi” -> 点击当前连接Wi-Fi旁的“详细信息...” -> 在“TCP/IP”标签页找到 `192.168.x.x` 格式的IP地址。
        -   在 Windows 上：打开“命令提示符(cmd)”，输入 `ipconfig`，在“无线局域网适配器 WLAN”下找到IPv4地址。

**第二步：在手机（或其他设备）上操作**

我们将以iPhone为例，Android和游戏机的设置路径类似。

1.  打开手机的“设置” -> “无线局域网”。
2.  点击您当前连接的Wi-Fi网络名称旁边的**信息图标 (i)**。
3.  滑到页面最底部，找到“配置代理”并点击进入。
4.  选择“**手动**”。
5.  在“**服务器**”栏，输入您在第一步中找到的**电脑的IP地址**。
6.  在“**端口**”栏，输入您在第一步中记下的**混合端口号**。
7.  点击右上角的“**存储**”。

**第三步：验证**

在您的手机上打开浏览器，尝试访问一个需要代理的网站。如果能成功访问，说明配置已经生效。当您不需要时，回到手机的Wi-Fi设置将代理改回“关闭”即可。

#### Q9: 如何关闭“允许局域网连接”功能？关闭后手机的代理还起作用吗？
**如何关闭**:
回到 `Clash Verge` 客户端的“设置 (Settings)” -> “常规 (General)”页面，**再次点击“允许局域网连接 (Allow LAN)”开关**，使其从开启状态变回关闭状态即可。

**是否还起作用**:
**一旦关闭，该功能会立即失效**。您手机上配置的代理将无法连接到电脑，从而无法上网。

**技术原理**:
- **开启时**: Clash服务会监听 `0.0.0.0:[端口号]`，这意味着它接受来自**任何网络接口**（包括局域网）的连接。
- **关闭时**: Clash服务会监听 `127.0.0.1:[端口号]` (即`localhost`)，这意味着它**只接受来自本机内部**的连接，所有来自局域网其他设备的请求都会被拒绝。

#### Q10: 如果我的配置文件中写了`allow-lan: false`，但我在客户端手动开启了开关，最终以哪个为准？
**结论：以您在客户端图形界面（GUI）上的实时操作为准。**

**工作原理**:
1.  **加载时 (初始设置)**: `Clash Verge`启动或重载配置时，会读取`.yaml`文件，将`allow-lan`的功能设置为`false`（关闭）。
2.  **运行时 (实时指令)**: 当您在GUI上手动开启开关时，客户端会向Clash核心发送一个**实时API指令**，要求将`allow-lan`的状态变更为`true`。这个运行时指令的优先级**高于**文件里的初始配置。

**重要提示**: 这种通过GUI的覆盖是**临时的**，仅对当前会话生效。如果您**重载配置**或**重启Clash Verge**，它会重新读取文件，配置将恢复为文件设定的`false`状态。

**最佳实践**:
- 若要**永久生效**，请直接修改 `.yaml` 配置文件。
- 若只是**临时调整**，使用GUI开关更方便。

#### Q11: 我有必要修改Clash的默认端口号吗？
这是一个非常实际的问题。对于绝大多数用户来说，**您完全没有必要，也不建议您更改Clash Verge默认的端口设置。**

**为什么通常不需要更改？**

1.  **约定俗成**: 默认端口（如SOCKS的`7890`）是整个Clash生态中一个广泛接受的标准。网络上绝大多数的教程、脚本和配置示例，在提到终端代理时，都会默认使用`7890`这个端口。保持默认可以确保您参考其他资料时无缝对接。
2.  **避免冲突**: 默认端口号（如`7890`, `7897`）都位于1024之后的高位端口范围，特意避开了其他常见软件（如数据库、Web服务器）的默认端口，因此它们与其他程序发生冲突的概率极低。

**什么情况下才【必须】更改？**

您唯一需要更改端口的场景，就是发生了**端口冲突**。

- **什么是端口冲突？** 当您启动`Clash Verge`时，如果您的电脑上已经有**另一个程序**正在使用（监听）`7890`这个端口，Clash就无法成功启动它的代理服务。
- **如何判断？** 如果发生端口冲突，Clash通常会在“日志 (Logs)”页面打印出明确的错误信息，内容通常包含 `address already in use` (地址已被使用) 等关键字。
- **如何解决？** 在这种罕见的情况下，您只需要在“设置 (Settings)” -> “网络 (Network)”中，将冲突的端口号（例如`7890`）修改为一个其他的、未被占用的数字（例如`7895`），然后保存即可。

**重要提醒：** 如果您因为端口冲突而修改了端口号，请务必记住，在其他任何地方使用它时也要同步修改，例如终端代理的环境变量设置。

#### Q12: 使用Clash后，访问国内网站会变慢吗？会限制我的网速吗？
这是一个非常核心的问题。结论是：**对于直连的国内访问，Clash的性能影响小到可以忽略不计，并且它绝不会限制您的网络带宽。**

下面是详细解释：

**1. 对网络延迟的影响：几乎为零**
当您访问一个国内网站时，Clash只在连接建立的**最开始瞬间**介入：
1.  **流量捕获**：请求被Clash接管。
2.  **规则匹配**：Clash以极高的速度（通常在1毫秒以内）查询规则，发现这是一个“直连”请求。
3.  **直接放行**：Clash立刻将这个连接“交还”给系统，后续的数据传输完全在Clash之外进行，由您的电脑和网站服务器直接通信。

这个决策过程所增加的延迟通常在**亚毫秒级别**，相比于几十毫秒的正常网络延迟，完全可以忽略。

**2. 对网络带宽的影响：无限制**
对于被判断为“直连”的流量，Clash仅做“决策”，不做“转发”。数据传输是您的电脑和网站服务器之间的“点对点”直通车，能跑多快，完全取决于您的**运营商带宽**和**对方服务器的能力**。Clash不会作为中间人去限制您的速度。

**3. 形象的比喻：小区门口的保安**
您可以把Clash想象成一个反应极快的保安：
- **访问国内网站（业主回家）**: 您开车到门口，保安（Clash）看了一眼车牌（规则匹配），立刻抬杆放行。这个识别和抬杆的过程对您回家的总时间几乎没影响。车开进小区后能跑多快，跟保安就没关系了。
- **访问国外网站（访客来访）**：访客要进来，保安需要拦下他，打电话确认、登记信息（加密和转发流量）。这个过程自然就慢一些，速度瓶颈在于电话信号和访客走路的速度（代理服务器的质量）。

**总结：** 对于国内访问，Clash只是一个高效的“抬杆保安”，其性能影响微乎其微，可以放心使用。

#### Q13: 为什么开启代理后，在VS Code里下载/更新扩展（特别是微软官方的）会非常慢或失败？
这是一个非常普遍的“代理绕路”问题。

**根本原因：好路不走，偏要绕远**
VS Code的扩展商店和下载服务器是微软的全球CDN网络。正常情况下，它会自动为您连接到离您最近、速度最快的服务器节点（如香港）。

当您开启代理后，由于默认规则会将这些不属于国内网站的请求发往您的代理服务器（如在新加坡），这就形成了一条低效的路径：`您的电脑 -> 新加坡代理 -> 微软CDN节点`。您放弃了直连最优节点的“高速公路”，因此速度剧降。

**解决方案：为微软服务设置“直通车”规则**
您需要在Clash的规则中，明确告诉它“所有访问微软官方服务的流量，都直接连接”。

**操作步骤 (SOP):**
1.  用文本编辑器打开您的 `.yaml` 配置文件。
2.  找到 `rules:` 列表。
3.  添加以下两条**直连规则**，建议放在所有`PROXY`规则之后，但在`GEOIP,CN,DIRECT`和`MATCH,PROXY`等通用规则之前。

    ```diff
     rules:
       # ... 其他已有的具体代理规则 ...
       - DOMAIN-SUFFIX,github.com,PROXY
       
      # --- 新增规则：让微软的服务走直连 ---
      - DOMAIN-SUFFIX,visualstudio.com,DIRECT
      - DOMAIN-SUFFIX,msecnd.net,DIRECT
      
      # --- 通用规则开始，新规则要放在这之上 ---
      - GEOIP,CN,CN-DIRECT
      - MATCH,PROXY
    ```
    > **说明**: `visualstudio.com`保证了商店浏览的流畅性，而`msecnd.net`是微软CDN的域名，**是解决下载速度最关键的一条规则**。

4.  保存文件，然后回到Clash Verge客户端，在“配置(Profiles)”页面**重载(Reload)**您的配置。

---

## 第四章：界面功能详解 (按菜单栏)

本章节将按照 `Clash Verge Rev` 左侧菜单栏的顺序，为您提供一份极其详尽的功能说明书，旨在帮助您完全掌握这款强大的工具。

### 1. 首页 (Dashboard)
这是Clash的信息中枢与控制中心，提供了所有核心状态的概览和快捷操作入口。

- #### 卡片：订阅 (Profiles)
    - **功能**: 这是您管理配置文件的快捷入口。
    - **使用**: 点击此卡片会直接跳转到“订阅”页面，方便您快速导入新的配置文件或更新现有订阅。

- #### 卡片：当前节点 (Current Connection)
    - **功能**: 显示当前正在使用的代理节点信息。
    - **解读**: 在“规则”模式下，这里通常只显示**默认策略组或全局策略组**最终选择的节点。由于每个网络请求都可能匹配到不同规则而走向不同节点，所以此处的显示**仅供参考**，不代表所有流量都通过该节点。要查看某个具体连接走了哪个节点，请使用“连接”页面。

- #### 卡片：网络设置 (Network)
    这是控制Clash核心功能的区域。
    - **系统代理 (System Proxy) 开关**:
        - **作用**: 这是Clash的**主开关**。开启后（变为彩色），Clash会修改您操作系统的网络设置，让浏览器、微信等大多数应用程序的流量都经过Clash进行处理。关闭后，Clash仅作为后台程序运行，不会影响任何网络连接。
        - **使用**: 日常使用“规则”模式时，此开关应保持开启。
    - **虚拟网卡模式 (TUN Mode)**:
        - **作用**: 一种更强大、更底层的代理模式。它会创建一张虚拟网卡来接管您电脑**所有**的IP流量。
        - **与系统代理的区别**: “系统代理”只影响那些“尊重”并查询系统代理设置的程序。而一些命令行工具、游戏、或行为不规范的软件可能会忽略系统代理。`TUN`模式则能强制捕获这些流量, 实现更彻底的全局代理。(更多深入信息，请参阅第五章)
        - **使用**: 当您发现“系统代理”对某个特定程序无效时，可以尝试开启`TUN`模式。开启后，“系统代理”开关通常会自动关闭。**注意：** 此模式可能会与VPN或其他网络软件冲突，初学者建议在需要时才开启。
    - **高级系统代理设置 (点击齿轮图标)**:
        点击“系统代理”开关右侧的齿轮图标，可以打开高级设置弹窗，其中包含以下选项：
        - **使用 PAC 模式 (Use PAC Mode)**: PAC (Proxy Auto-Config) 是一种通过脚本自动配置代理的方式。当您勾选此项时，Clash Verge 会根据您当前的规则，自动生成一个 PAC 脚本让系统使用。这是一个**兼容性选项**，主要用于某些只支持 PAC 代理的老旧程序。对于绝大多数现代应用，**您无需开启此模式**，Clash 原生的“规则”模式是更优越的选择。
        - **系统代理守卫 (System Proxy Guard)**: 这是一个**保护性**功能。开启后，Clash Verge 会定期检查并强制修正您电脑的系统代理设置，防止它被其他程序（如某些网银客户端）意外篡改。如果您的代理工作稳定，则无需开启。
        - **始终使用默认绕过 (Bypass Domain/IPNet)**: 该列表定义了一组**永远不走代理**的地址，例如 `localhost` (本机地址) 和 `***********/16` (局域网地址)。这确保了您对本地网络设备的访问不会被错误地发送到代理服务器。**强烈建议始终保持此选项开启**。

- #### 卡片：代理模式 (Mode)
    - **功能**: 快速切换Clash的三种核心工作模式。
    - **规则 (Rule)**: **默认和推荐模式**。根据配置文件中的规则列表，智能判断流量走向（部分走代理，部分直连）。
    - **全局 (Global)**: **强制所有流量**都通过“代理”页面中`GLOBAL`策略组所选定的那个节点。适合临时需要所有流量都走代理的场景。
    - **直连 (Direct)**: **强制所有流量**都不走代理。适合临时需要关闭所有代理的场景。

- #### 卡片：流量统计 (Traffic)
    - **功能**: 实时显示当前网络的上传/下载速度，以及自软件启动以来累计使用的流量。

### 2. 代理 (Proxies)
这是Clash的“手动挡”控制中心，用于管理和切换所有**策略组 (Proxy Groups)** 的行为。

- #### 策略组的类型
  您的配置文件中通常包含以下几种策略组，它们在“代理”页面有不同的交互方式：
  - **`select` (手动选择)**: 允许您从其包含的节点/策略组列表中，手动点击选择一个。这是最常见的类型，用于需要手动切换线路的场景。
  - **`url-test` (自动测速)**: 它会自动对其包含的所有节点进行延迟测试，并**自动选择延迟最低（速度最快）的那个节点**。您也可以手动覆盖它的选择。
  - **`fallback` (故障转移)**: 它会按照列表顺序，使用第一个可用的节点。当该节点连接失败时，**自动切换到下一个可用的节点**。
  - **`load-balance` (负载均衡)**: 将流量**随机分配**到其包含的各个节点上，适用于希望流量分散的少数场景。

- #### 节点交互
  - **选择节点**: 在`select`类型的策略组中，直接点击您想要的节点即可切换。
  - **测试延迟**: 点击策略组或单个节点右侧的**闪电图标**，可以手动触发一次延迟测试。节点名称右侧会显示具体的毫秒数（ms）或`timeout`（超时）。

- #### 内置策略
  - **`DIRECT` (直连)**: 一个特殊的内置节点，选择它意味着匹配到此策略组的流量将直接连接，不走代理。
  - **`REJECT` (拒绝)**: 另一个内置节点，选择它意味着匹配到此策略组的流量将被直接阻断，常用于屏蔽广告。

### 3. 订阅 (Profiles)
这是您管理所有**配置文件**的地方，无论是来自URL订阅还是本地文件。

- **导入**:
  - **URL订阅**: 在顶部的输入框粘贴“订阅URL”，然后点击导入。这是最常见的方式。
  - **本地文件导入**: 除了最直接的**拖拽法**（将`.yaml`文件拖入窗口）外，还有两种常用方法：
    1.  **复制粘贴法 (推荐)**: 用文本编辑器打开 `.yaml` 文件并复制其全部内容。然后在 Clash Verge 的“订阅”页面点击“新建(New)”，将内容粘贴到内置编辑器中并保存。这个方法无视路径问题，最为可靠。
    2.  **本地服务器法**: 在文件所在目录用终端开启一个 `python3 -m http.server 8000` 服务，然后在 Clash Verge 的订阅输入框中填入 `http://127.0.0.1:8000/文件名.yaml` 进行订阅。

- **激活**: 在列表中**单击**某一个配置文件，使其前方出现**蓝色竖条**，代表该配置已在Clash中生效。
- **更新**: 点击配置文件右侧的**更新(Update)**图标（环形箭头），可以从订阅URL拉取最新的服务器列表和规则。
- **测速**: 点击**测试(Test)**图标（闪电），可以对该配置文件下的所有节点进行一次延迟测试。
- **编辑**: 点击**编辑(Edit)**图标（笔），可以用系统默认的文本编辑器打开该`.yaml`文件，方便您进行手动修改。

### 4. 连接 (Connections)
这是进行网络问题排查和流量分析的“神器”。

- **信息解读**:
  - **Host**: 访问的目标地址（域名或IP）。
  - **Network**: 使用的网络协议（TCP/UDP）。
  - **Type**: 连接的来源类型（如HTTP, HTTPS）。
  - **Chain**: 显示完整的连接链。例如 `[TCP] -> 节点A -> [VLESS] -> 目标地址`。
  - **Rule**: 这条连接具体命中了配置文件中的哪一条规则。
  - **Proxy**: 最终它走了哪个代理节点，或者是`DIRECT`。
  - **Time**: 连接建立的时间。
- **交互操作**:
  - **搜索/过滤**: 在顶部的搜索框中输入关键字，可以快速筛选出您关心的连接。
  - **关闭连接**: 将鼠标悬浮在某条连接上，点击右侧出现的**红色断开图标**，可以手动中断该连接。

### 5. 规则 (Rules)
这里以**只读**的方式，展示了您当前已激活的配置文件中包含的所有规则列表及其匹配顺序。主要用于帮助您理解当前配置文件的分流逻辑，进行高级分析。

### 6. 日志 (Logs)
这里是Clash运行日志的实时输出窗口，是进行高级故障排查的地方。

- **日志级别**:
  - **`info`**: 默认级别，显示常规信息。
  - **`warning`**: 显示潜在的问题。
  - **`error`**: 显示导致操作失败的错误。
  - **`debug`**: 显示非常详细的调试信息，用于深度排查。
  - **`silent`**: 不输出任何日志。
- **使用**: 当Clash无法启动、配置加载失败、或某个节点反复断线时，来这里查看详细的错误信息，可以帮助您或服务商定位问题。

### 7. 测试 (Test)
此功能用于批量测试代理节点的可用性。

- **延迟类型**:
    - **TCPing**: 通过TCP握手测试延迟，最常用。
    - **ICMP**: 即我们常说的`ping`，部分服务器可能禁ping。
- **URL测试**: 您可以设置一个测试URL（如`http://www.google.com/generate_204`），Clash会通过每个节点访问该URL来判断其连通性。这比单纯的延迟测试更准确。
- **结果解读**:
    - **绿色(ms)**: 表示节点可用，数字为延迟。
    - **红色(timeout)**: 表示节点连接超时，不可用。

### 8. 设置 (Settings)
这里是`Clash Verge`的全局设置中心，所有自定义配置都在此进行。

- #### 标签页：常规 (General)
  - **开机自启 (Start with OS)**: 勾选后，Clash Verge将在您登录系统时自动启动。
  - **语言 (Language)**: 切换界面语言。
  - **允许局域网连接 (Allow LAN)**: **非常实用的功能**。打开后，同一WiFi下的其他设备（如手机、平板、游戏机）可以通过您电脑的局域网IP和代理端口，来使用Clash的代理服务，实现全局共享。
  - **主题 (Theme)**: 切换浅色/深色/系统默认主题。

- #### 标签页：配置文件 (Profiles)
  - **自动更新 (Auto Update)**: 设置订阅链接的自动更新间隔（如每24小时），确保节点信息保持最新。
  - **预处理配置 (Parse Loacl File)**: 这是一个高级功能，允许您在导入本地配置文件前，用一个脚本对其进行预处理，例如动态添加一些自定义规则。

- #### 标签页：网络 (Network)
  - **代理端口 (Proxy Port)**: 查看或修改Clash在本地监听的代理端口号。
    - **SOCKS Port**: SOCKS5代理端口。
    - **HTTP Port**: HTTP代理端口。
    - **Mixed Port**: 混合代理端口，同时支持SOCKS5和HTTP。**终端代理环境变量中的端口号必须与这里显示的端口号之一保持一致。**
  - **TUN模式设置 (TUN Mode Settings)**:
    - **堆栈 (Stack)**: 选择TUN模式使用的网络协议栈，不同选项对性能和兼容性有影响，通常保持默认即可。
    - **DNS劫持 (DNS Hijacking)**: 将所有DNS查询强制重定向到Clash内置的DNS服务器，避免DNS污染，增强代理效果。 

---
## 第五章：进阶主题 - 深入理解 TUN 模式与连接安全

本章节旨在深入探讨一些高级用户关心的核心问题，帮助您不仅知其然，更知其所以然。

### 5.1 虚拟网卡 (TUN) 模式深度解析

#### 它是如何工作的？技术原理揭秘

虚拟网卡 (TUN) 模式是一种更强大、更底层的代理技术。它的工作完全发生在您的设备本地，通过修改操作系统核心的网络配置，来创建一个“设备级”的流量调度中心。它与您的Wi-Fi路由器或任何上游网络设备无关。

其核心技术依赖于两项操作系统层面的关键机制：

1.  **创建虚拟网卡 (TUN Interface)**
    当代理软件启动TUN模式时，它会向您的操作系统（无论是macOS, Windows还是Linux）申请创建一个**“虚拟网络接口”**。这个接口并非物理硬件，而是由软件在内存中模拟的，它在操作系统层面被视作一个真正的网卡，通常命名为 `utunX` (在macOS/iOS上)。

2.  **操控路由表 (Routing Table Manipulation)**
    这是最关键的一步。您的操作系统维护着一张“IP路由表”，这张表是网络交通的导航地图，规定了所有数据包应该从哪个“门”（网络接口）发出去。开启TUN模式的本质，就是代理软件修改了这张地图的“默认路线”，添加了高优先级的规则，将原本应直接发往物理网卡（如 `en0` for Wi-Fi）的流量，**强制转向**到那个新创建的 `utunX` 虚拟网卡上。

#### 开启 TUN 模式后，数据包的完整旅程

1.  **应用发出请求**：您的浏览器想访问 `google.com`。
2.  **系统查阅路标**：操作系统查询路由表，发现最高优先级的规则指向 `utunX` 虚拟网卡。
3.  **流量进入虚拟通道**：操作系统将数据包发送给 `utunX` 接口。
4.  **代理软件处理**：代理软件作为 `utunX` 的管理者，接收到这个数据包。它根据您设定的规则（例如，`google.com`走代理）进行加密、转发等处理。
5.  **处理后交还系统**：代理软件将处理好的数据包，通过正常途径交还给操作系统内核。
6.  **物理网卡发出**：这一次，操作系统根据默认的物理路由（`default` -> `en0`），将这个已经被代理处理过的、准备发往互联网的数据包通过您的Wi-Fi网卡发送给路由器。

这个**完全在设备内部形成的闭环**，实现了对所有应用流量的“透明代理”。

#### 与“系统代理”模式的关键区别

| 特性 | **系统代理 (System Proxy)** | **虚拟网卡模式 (TUN Mode)** |
| :--- | :--- | :--- |
| **工作层面** | 应用层 | 网络层 (更底层) |
| **影响范围** | 只影响那些“尊重”并查询系统代理设置的程序（如浏览器、大部分App）。 | 强制接管**所有**程序的 IP 流量，无论程序本身是否支持代理。 |
| **典型场景** | 日常网页浏览、聊天软件等。 | 命令行工具、游戏、行为不规范的软件、或任何“系统代理”无效的场景。 |
| **终端配置** | **是** (必须为终端单独配置环境变量) | **否** (完全无需配置，一劳永逸) |

简单来说：
- **系统代理**像是一个“建议”。它告诉所有程序：“各位，如果你们想上网，建议来我这里（Clash）报到一下。” 大部分程序会听从这个建议，但终端等工具会忽略它。
- **虚拟网卡模式**则是一个“强制命令”。它直接在网络道路的唯一出口处设了一个关卡，不管是什么程序产生的流量，都必须从这里经过并接受检查和调度。

#### 开启 TUN 模式后，终端还需要配置吗？
**完全不需要。** 这正是 TUN 模式相比系统代理最核心的优势之一。

由于 TUN 模式是在网络层强制接管了所有流量，因此当您在终端里运行任何命令（如 `gemini`、`git`、`curl`）时，它们产生的网络请求在离开电脑前就会被虚拟网卡捕获，并交由 Clash 内核处理。您之前为终端设置的 `export ALL_PROXY=...` 环境变量在这种模式下可以安全地移除了。

### 5.2 TUN 模式安全吗？会永久修改我的系统吗？
**答案是：完全安全，并且过程完全可逆，不会对系统造成永久性影响。**

您可以将 Clash Verge 的这个行为理解成一个非常严谨的“借用与归还”过程：

1.  **“借用”阶段（开启 TUN 模式）：**
    -   当您点击开启“虚拟网卡模式”时，Clash 内核会向您的操作系统发出请求：“你好，系统，我想临时接管一下网络。请把默认的路由表（即网络交通规则）先**备份**一下，然后修改它，让所有流量都先经过我创建的这张虚拟网卡。”
    -   操作系统批准后，Clash 就开始根据这个临时修改的路由表来处理您的所有网络流量。

2.  **“归还”阶段（禁用 TUN 模式）：**
    -   当您再次点击开关以禁用“虚拟网卡模式”，或者正常退出 `Clash Verge` 程序时，Clash 内核会执行一个**清理程序**。
    -   这个清理程序会向操作系统发出指令：“你好，系统，我的工作完成了。请把我创建的虚拟网卡删除，并把我之前让您**备份的那个原始路由表恢复回来**。”
    -   操作系统执行指令后，您的网络设置就完全回到了 `Clash` 接管之前的状态。

**如果程序意外崩溃了怎么办？**
在极少数情况下，如果因为程序或系统严重错误导致路由表未能恢复，您只需**重启电脑**即可。电脑重启会强制重新初始化整个网络服务和路由表，100%能将其恢复到最原始的、正确的状态。

### 5.3 连接安全性分析：我的流量会被看到吗？

#### 开启 TUN 模式后，规则还生效吗？
**绝对生效。** 并且这正是 TUN 模式强大的原因。

“系统代理”和“虚拟网卡”只是两种不同的**“流量捕捉方式”**。无论使用哪种方式捕捉到流量，这些流量最终都会被送到**同一个处理中心**——即 Clash 的核心引擎。而该引擎的唯一工作准则，就是您已激活的配置文件中的 **`rules:` (规则) 列表**。

#### 我的访问会被检测出流向和内容吗？
对于外部的“检测工具”来说，**极其困难**。这主要得益于您服务器上部署的 `VLESS + Reality` 方案。

我们将您的访问链路分为两段来看：

1.  **从您的电脑 -> 到您的 Xray 服务器**
    -   **流量内容**：您访问 `google.com` 的请求内容，被 `VLESS` 协议**高强度加密**了。
    -   **流量特征**：`Reality` 协议发挥了“伪装”作用。它使得这段加密流量在外界（例如网络运营商）看来，与一个正常的、访问 `www.bing.com` (您在配置中指定的伪装目标) 的标准 HTTPS 流量**几乎没有区别**。它没有传统代理的明显“指纹”，从而可以有效抵抗主动探测和流量识别。

2.  **从您的 Xray 服务器 -> 到 Google**
    -   当您的请求到达服务器后，服务器会解密您的请求，然后以它自己的名义去访问 Google。由于 Google 本身是 HTTPS 加密的，所以这段连接也受到标准加密保护。

**总结一下，不同的“观察者”能看到的信息是：**
| 观察者 | 能看到的信息 |
| :--- | :--- |
| **网络运营商/检测工具** | 您的电脑与您的服务器IP进行了一次看似访问 `www.bing.com` 的普通加密通信。 |
| **您的 Xray 服务器** | 您通过它访问了 `google.com` 这个域名。 |
| **Google** | 它的服务被一个来自您服务器IP的客户端访问了，并不知道最初是来自您。 |

#### 一个发往新加坡的 `www.bing.com` 请求，不会很可疑吗？
**恰恰相反，这是一种完全正常、极其普遍的网络行为。**

这背后的原因是现代互联网广泛使用的 **CDN（内容分发网络）** 和 **Anycast（任播）** 技术。

-   像微软 (`Bing`) 这样的全球性公司，为了给所有用户提供最快服务，会在世界各地（包括新加坡这样的重要网络枢纽）部署大量服务器节点。
-   当您访问 `www.bing.com` 时，您的请求不一定会发送到离您最近的服务器，而是会被智能路由到一个当前网络质量“最优”的节点，这个节点完全可能在新加坡。
-   因此，检测工具看到您的电脑正在访问一个位于新加坡云服务商（例如腾讯云）IP 上的 `www.bing.com` 服务，会认为这只是一次司空见惯的“跨国 CDN 访问”，是完全合理、正常的业务流量，从而安全地将其放行。

您的代理流量正是通过伪装成这种高频发生的正常行为，从而实现了“大隐隐于市”的效果。

### 5.4 附录：如何亲眼观察路由表变化？

如果您想亲眼验证“虚拟网卡模式”是如何修改系统路由表的，可以在 macOS 的“终端”中通过一个简单的命令来观察。其禁用后，路由表也会安全地恢复原状，不会影响正常使用。

**1. 查看命令**

在“终端”应用中，输入以下命令并按回车：
```bash
netstat -r
```

**2. 解读输出内容**

您会看到一个包含多列的路由表，我们主要关注以下几列：
- **`Destination` (目标地址)**：网络请求的目标网段。`default` 代表“所有其他未明确指定的流量”。
- **`Gateway` (网关)**：数据包为了到达目标，需要经过的“下一跳”地址，通常是您的路由器IP。
- **`Netif` (网络接口)**：数据包将通过哪一个物理或虚拟网卡发送出去。例如 `en0` (物理网卡) 或 `utunX` (虚拟网卡)。

**3. 对比不同状态下的路由表**

- #### **TUN 模式关闭时 (正常状态)**
    `default` 路由的 `Gateway` 通常是您的路由器地址，`Netif` 是 `en0`。
    > **含义**：所有流量都通过您的物理网卡（如Wi-Fi），发往路由器。

- #### **TUN 模式开启时**
    路由表会被修改，`default` 规则（或等效的 `0.0.0.0/1` 和 `*********/1`）的 `Netif` 会指向一个名为 `utunX` 的虚拟接口。
    > **含义**：所有流量都被强制先发送到 Clash 创建的 `utunX` 虚拟接口进行处理。

通过在开启和关闭 TUN 模式时分别执行此命令，您就能非常直观地理解其“强制接管”所有网络流量的工作原理。 