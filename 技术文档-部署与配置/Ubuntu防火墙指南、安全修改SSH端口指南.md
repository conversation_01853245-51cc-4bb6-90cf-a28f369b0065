# 备忘录：在现代Ubuntu服务器上安全修改SSH端口

本文档旨在为您提供一个安全、可靠的标准作业流程（SOP），用于在云服务器（如腾讯云）上的现代 Ubuntu 系统中修改 SSH 默认端口。它包含了此过程中常见的陷阱和正确的处理方法。

---

## 1. 核心概念与陷阱解析

在开始操作前，必须理解以下两个核心概念，这能解释我们之前遇到的所有问题。

### 概念一：双层防火墙（纵深防御）

您的云服务器有两层独立的防火墙，它们像串联的门锁，必须同时开启，流量才能进入。

*   **外层防火墙（小区大门）：云服务商的安全组**
    *   这是在您的服务器之外，由腾讯云等云服务商提供的网络层防火墙。
    *   它控制着哪些流量可以到达您的服务器。如果它阻止了一个端口，任何请求都无法触及您的操作系统。

*   **内层防火墙（自家房门）：操作系统防火墙 (UFW)**
    *   这是运行在您的 Ubuntu 系统内部的软件防火墙。
    *   它负责控制已经通过外层防火墙的流量，是否被允许访问具体的服务程序。

**结论：** 任何端口的访问，都必须**同时**在**安全组**和 **UFW** 中被设置为“允许”，否则连接必然失败。

### 概念二：Systemd 套接字激活 (Socket Activation)

这是我们遇到的 `Connection refused` 问题的根本原因。

*   **旧的工作方式：** `sshd` 程序自己负责监听配置文件 (`/etc/ssh/sshd_config`) 中指定的端口。
*   **现代的工作方式 (Ubuntu 20.04+):** 为了效率和按需启动，系统引入了`systemd`。一个名为 `ssh.socket` 的单元**接管了监听端口的职责**。`systemd` 先监听端口，当有连接请求进来时，它再唤醒 `sshd` 服务去处理。

**结论：** 在这种模式下，`/etc/ssh/sshd_config` 文件中的 `Port` 指令会被**完全忽略**。修改端口必须去修改 `ssh.socket` 的配置。

**重要补充：`sshd_config` 的实际作用**

既然端口由 `systemd` 管理，为何 `/etc/ssh/sshd_config` 文件依然重要？

因为它们的职责是分开的：
- **`ssh.socket`** 只负责 **“在哪个端口监听”**。
- **`sshd_config`** 负责 **“监听之后的所有事”**，这包括了所有关键的安全配置，例如：
    - `PermitRootLogin no` (禁止 root 登录)
    - `PasswordAuthentication no` (禁用密码认证)
    - `PubkeyAuthentication yes` (启用公钥认证)
    - 其他所有加密算法、连接参数等。

正因为 `sshd_config` 文件保管着这些核心安全设置，所以在系统升级 `openssh-server` 并提示该文件被修改时，我们**必须选择“保留本地版本” (`keep the local version currently installed`)**，以确保我们的安全加固不会被新版的默认配置覆盖。

---

## 2. 安全修改SSH端口的标准流程（SOP）

本流程遵循“先开后关，分步验证”的绝对安全原则。假设我们将 SSH 端口从 `22` 修改为 `28182`。

### 第1步：在“外层防火墙”为新端口开门

1.  登录您的腾讯云控制台。
2.  进入“防火墙”（或“安全组”）管理界面。
3.  **添加一条新规则**：
    *   **应用类型/协议:** `TCP`
    *   **端口:** `28182`
    *   **来源:** `全部IPv4地址` 或 `0.0.0.0/0`
    *   **策略:** `允许`
4.  **！！！关键：此时不要删除旧的 `22` 端口规则。**

### 第2步：在“内层防火墙”为新端口开门

通过当前可用的 SSH 连接登录服务器，执行以下命令。

```bash
# 为新端口 28182 添加入站规则
sudo ufw allow 28182/tcp

# 检查状态，确保新旧两个端口（22 和 28182）都处于 ALLOW IN 状态
sudo ufw status
```

### 第3步：修改 Systemd 套接字配置（正确的方法）

这是修改端口的核心步骤。

1.  为 `ssh.socket` 创建一个覆盖配置目录和文件：
    ```bash
    sudo mkdir -p /etc/systemd/system/ssh.socket.d/
    sudo nano /etc/systemd/system/ssh.socket.d/listen.conf
    ```
2.  将以下内容完整粘贴到 `listen.conf` 文件中：
    ```ini
    [Socket]
    ListenStream=
    ListenStream=28182
    ```
    *   `ListenStream=` (留空) 用于清空默认的 `ListenStream=22` 设置。
    *   `ListenStream=28182` 用于设定我们想要的新端口。

3.  按 `Ctrl+X` -> `Y` -> `Enter` 保存并退出。

### 第4步：应用新配置并重启服务

```bash
# 让 systemd 重新加载所有配置文件
sudo systemctl daemon-reload

# 重启 socket 和 service 以应用新端口
sudo systemctl restart ssh.socket
sudo systemctl restart ssh.service
```

### 第5步：验证新端口连接（最关键的一步）

1.  **保持当前的终端连接不要关闭！这是您的安全后路！**
2.  **在您的电脑上，打开一个全新的终端窗口。**
3.  使用 `-p` 参数指定新端口，尝试登录：
    ```bash
    ssh your_user@your_server_ip -p 28182
    ```
4.  **结果确认：**
    *   **成功登录：** 证明所有步骤均正确。可以继续下一步。
    *   **失败：** 请勿惊慌。切换回老的终端窗口，使用下面的“问题排查”指南进行诊断。

### 第6步：清理旧端口（确认成功后）

在**已经通过新端口 `28182` 成功登录**的终端会话中，执行以下清理工作。

1.  **关闭内层防火墙的旧端口：**
    ```bash
    sudo ufw delete allow 22/tcp
    ```
2.  **关闭外层防火墙的旧端口：**
    回到腾讯云控制台，删除安全组中关于端口 `22` 的那条规则。

至此，您已安全、彻底地完成了 SSH 端口的更换。

---

## 3. 问题排查指南

*   **错误信息: `Connection timed out`**
    *   **含义：** 连接请求在到达服务器的途中被丢弃了。
    *   **原因：** 99%是防火墙问题。请检查**腾讯云安全组**和服务器**UFW**是否都已正确放行了目标端口。

*   **错误信息: `Connection refused`**
    *   **含义：** 连接请求已到达服务器，但服务器上没有任何程序在监听该端口。
    *   **原因：** 防火墙配置正确，但 SSH 服务本身没有在目标端口上监听。这通常指向 `systemd` 配置问题。

*   **关键诊断命令：**
    *   此命令可以查看 `sshd` 或 `systemd` 当前真正在监听哪个端口，是判断问题根源的最终证据。
    ```bash
    sudo ss -tlpn | grep ssh
    ```

---

## 4. 附录：UFW 防火墙常用命令详解

本章节提供对 Ubuntu 内置防火墙 `UFW` (Uncomplicated Firewall) 常用命令的详细解释，作为日常管理的备忘。

### 4.1 核心控制

| 命令 | 作用 |
| --- | --- |
| `sudo ufw enable` | **启用防火墙**并设置为开机自启。**高危警告：** 执行前必须确保已放行当前使用的 SSH 端口！ |
| `sudo ufw disable`| **禁用防火墙**。通常仅在调试时使用。 |
| `sudo ufw reload` | **重载防火墙规则**。在手动修改了位于 `/etc/ufw/` 的配置文件后使用，日常通过命令增删规则无需此操作。 |

### 4.2 查看状态

| 命令 | 效果 |
| --- | --- |
| `sudo ufw status` | 查看防火墙的**基本状态**（激活/未激活）和规则列表的简洁视图。 |
| `sudo ufw status verbose` | 查看**详细状态**，包含日志、默认策略等所有信息。 |
| `sudo ufw status numbered` | **强力推荐！** 查看规则列表，并在每条规则前显示一个唯一的**编号**。这个编号在精确删除规则时至关重要。 |

### 4.3 规则管理：增加与删除

#### 添加规则 (`allow` / `deny`)

- **允许特定端口和协议（推荐用法）**
  ```bash
  # 精确地只允许 TCP 协议通过 28182 端口
  sudo ufw allow 28182/tcp
  ```
- **允许一个服务名称**
  ```bash
  # UFW 会自动从 /etc/services 文件查找 ssh 对应的默认端口 (22)，并创建规则
  sudo ufw allow ssh
  ```
- **拒绝特定端口访问**
  ```bash
  # 拒绝所有到 8080 端口的访问
  sudo ufw deny 8080
  ```

#### 删除规则 (`delete`)

**强烈推荐：按编号删除（最安全、最清晰的方法）**

这种方法可以避免因规则内容复杂而写错命令。

1.  **第一步：列出带编号的规则**
    ```bash
    sudo ufw status numbered
    ```
    您会看到类似下面的输出：
    ```
    Status: active
         To                         Action      From
         --                         ------      ----
    [ 1] 28182/tcp                  ALLOW IN    Anywhere
    [ 2] 22/tcp                     ALLOW IN    Anywhere
    [ 3] 28182/tcp (v6)             ALLOW IN    Anywhere (v6)
    [ 4] 22/tcp (v6)                ALLOW IN    Anywhere (v6)
    ```

2.  **第二步：根据编号删除指定的规则**
    例如，我们要删除旧的 `22/tcp` 规则，它对应的编号是 `[2]` 和 `[4]`。
    ```bash
    # 删除编号为 4 的规则 (先从编号大的删起，可避免列表重新编号带来的困扰)
    sudo ufw delete 4

    # 再删除编号为 2 的规则
    sudo ufw delete 2
    ```
    **技巧：** 从规则列表的**最大编号开始往前删除**，可以避免每删除一条规则后，剩余规则的编号就发生变化的问题。

**备用方法：按规则内容删除**
这种方法比较繁琐且容易出错，仅在特定场景下使用。
```bash
# 删除一条内容完全匹配 'allow 22/tcp' 的规则
sudo ufw delete allow 22/tcp
```

## 5. 附录：通过云控制台上传SSH公钥

在某些特殊情况下，例如本地网络限制无法使用 `ssh-copy-id`，或者在修复服务器时需要添加备用密钥，您可以通过云服务商提供的网页控制台（VNC）来手动上传公钥。

本流程完全绕过远程SSH，直接在服务器上进行操作。

### 第1步：在本地电脑获取公钥

1.  打开您的**本地电脑**终端。
2.  执行 `cat ~/.ssh/id_ed25519.pub` 或 `cat ~/.ssh/id_rsa.pub` 命令。
3.  **完整地复制**输出的那一长串以 `ssh-ed25519` 或 `ssh-rsa` 开头的公钥内容。

### 第2步：通过腾讯云VNC控制台粘贴公钥

1.  登录腾讯云，进入轻量应用服务器的“VNC登录”或“OrcaTerm登录”网页终端。
2.  使用您的**用户名和密码**登录服务器。**请确保登录的是您希望配置密钥的普通用户**，而不是 `root`。
3.  登录后，依次执行以下命令：

    a. **创建 `.ssh` 目录并设置权限（如果不存在）：**
       ```bash
       mkdir -p ~/.ssh && chmod 700 ~/.ssh
       ```

    b. **将复制的公钥追加到授权文件中：**
       推荐使用 `echo` 命令，以避免手动编辑文件可能产生的错误。
       ```bash
       echo "在此处粘贴您本地复制的完整公钥" >> ~/.ssh/authorized_keys
       ```

    c. **为授权文件设置严格权限（关键步骤）：**
       ```bash
       chmod 600 ~/.ssh/authorized_keys
       ```
       如果权限不正确，SSH 服务将拒绝使用此文件。

### 第3步：验证

操作完成后，打开一个全新的本地终端窗口，尝试通过 SSH 密钥直接登录。如果成功，则证明配置无误。

---

## 6. 附录：SSH 密钥密码遗忘的恢复流程

如果您忘记了为 SSH 私钥设置的密码 (Passphrase)，将无法再使用该密钥进行登录。但只要您还能通过其他方式（如本篇附录5所述的云控制台VNC）登录服务器，就可以安全地重置它。

**核心思路：** 密钥的密码无法找回，我们只能弃用旧密钥，换上一套新密钥。

### 第1步：在本地电脑生成新密钥

1.  **（可选）备份旧密钥**：在本地终端将忘了密码的旧密钥文件改名。
    ```bash
    mv ~/.ssh/id_ed25519 ~/.ssh/id_ed25519_forgotten
    ```
2.  **生成新密钥**：在本地终端重新生成一对密钥，并设置一个您能记住的新密码。
    ```bash
    ssh-keygen -t ed25519 -C "your_new_key_comment"
    ```

### 第2步：在本地电脑复制新公钥

执行 `cat ~/.ssh/id_ed25519.pub` 并完整复制输出的全部内容。

### 第3步：通过云控制台更新服务器

1.  通过 VNC 等方式登录您的服务器（使用用户名和服务器登录密码）。
2.  打开授权文件：`nano ~/.ssh/authorized_keys`。
3.  **删除旧公钥**：找到并删除代表您旧密钥的那一行。
4.  **添加新公钥**：将您在第2步复制的新公钥内容，完整地粘贴为新的一行。
5.  保存并退出文件。

### 第4步：验证

在本地电脑打开一个新终端，尝试用新的密钥和密码登录服务器。如果成功，则问题已解决。 