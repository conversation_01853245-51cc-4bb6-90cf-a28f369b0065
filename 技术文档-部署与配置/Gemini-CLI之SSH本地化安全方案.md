# 方案文档：在本地安全、稳定地使用 Gemini CLI

## 1. 需求与目标

本项目旨在解决在中国大陆地区，如何在保证数据传输安全、连接稳定、个人认证流程顺畅的前提下，于本地开发环境中无缝使用 `google-gemini/gemini-cli` 工具。

**核心要求如下：**

- **本地化运行**：`gemini-cli` 必须在用户的本地电脑（macOS）上运行，以便直接与本地项目文件交互。
- **个人认证**：必须能够顺畅完成 Google 个人账户的认证流程，以获取更高的免费使用额度。
- **连接与数据安全**：所有从本地到海外服务器，以及从服务器到 Google 服务的数据流都必须经过高强度加密。
- **访问安全**：服务器的访问必须得到严格控制，确保“只有我能连接”，同时要解决个人用户动态公网IP（IPv4/IPv6）带来的挑战，不依赖脆弱的IP白名单。
- **环境纯净**：本地电脑除了必要的浏览器插件外，不安装任何额外的命令行工具或图形化客户端程序。

## 2. 技术选型

### 2.1. 服务器选择：腾讯云轻量应用服务器

- **服务商**：腾讯云。
- **产品**：轻量应用服务器 (Lighthouse)。
- **地域**：**亚太地区**，如 **新加坡**、**东京（日本）**、**首尔（韩国）**。这些地域距离近，网络延迟较低，且能稳定访问 Google 相关服务。
- **理由**：该产品性价比高，提供固定月度流量包，管理界面简洁，防火墙配置直观，非常适合个人开发者用于搭建代理或轻量级应用。

### 2.2. 操作系统选择：Ubuntu 24.04 LTS

- **系统**：Ubuntu Server 24.04 LTS (长期支持版)。
- **理由**：
    - **稳定与现代**：LTS 版本提供长达5年的官方支持，系统稳定可靠。同时，24.04版本包含了较新的软件包和内核，性能更佳。
    - **社区广泛**：拥有庞大的用户社区和丰富的文档资料，遇到问题时容易找到解决方案。
    - **软件兼容性**：我们后续需要用到的 `Fail2Ban` 等工具在 Ubuntu 上有良好支持。

## 3. 主方案：SSH 深度强化方案 (终极纯净与安全)

本方案完全遵循“本地零安装”原则，通过对服务器进行多层安全加固，实现最高级别的安全保障。

---

### **第一阶段：服务器堡垒化加固**

目标：将一台新服务器转变为一个只有您能通过高强度密钥从任意网络位置安全访问的堡垒。

#### **步骤 1.1: 购买服务器并首次登录**
1.  前往腾讯云官网，购买一台符合上述配置的轻量应用服务器。
2.  在控制台重置密码（此密码仅用于首次登录），获取服务器的公网 IP 地址。
3.  在您的本地 Mac 终端，使用初始密码首次登录服务器（以 root 用户身份）：
    ```bash
    ssh root@[您的服务器公网IP]
    ```

#### **步骤 1.2: 创建并授权个人用户**
1.  创建一个供日常使用的个人用户（例如 `myuser`）：
    ```bash
    adduser myuser
    ```
2.  将该用户添加到 `sudo` 组，以便执行管理员命令：
    ```bash
    usermod -aG sudo myuser
    ```

#### **步骤 1.3: 配置高强度SSH密钥认证**
1.  **在本地 Mac 上**，生成一对高强度 `Ed25519` 密钥。它比传统的 RSA 密钥更安全、更高效。
    ```bash
    ssh-keygen -t ed25519 -C "<EMAIL>"
    ```
    一路回车即可，无需设置密码。
2.  **在本地 Mac 上**，读取并复制您的公钥内容：
    ```bash
    cat ~/.ssh/id_ed25519.pub
    ```
3.  **回到服务器终端**，切换到新创建的用户：
    ```bash
    su - myuser
    ```
4.  创建 `.ssh` 目录并设置严格的权限：
    ```bash
    mkdir ~/.ssh
    chmod 700 ~/.ssh
    ```
5.  将您复制的公钥粘贴到服务器的授权文件中，并设置权限：
    ```bash
    nano ~/.ssh/authorized_keys
    # 粘贴公钥内容，然后按 Ctrl+X, Y, Enter 保存退出
    chmod 600 ~/.ssh/authorized_keys
    ```
6.  现在，**打开一个新的本地 Mac 终端窗口**，尝试用密钥登录，确认配置成功：
    ```bash
    ssh myuser@[您的服务器公网IP]
    ```
    如果无需密码直接登录成功，则配置正确。

#### **步骤 1.4: 强化SSH服务（改端口、禁密码）**
1.  **在服务器上**，以 `myuser` 身份编辑 SSH 配置文件：
    ```bash
    sudo nano /etc/ssh/sshd_config
    ```
2.  进行以下关键修改，以隐藏服务、禁用不安全的登录方式：
    ```diff
    # 将默认的 22 端口改为一个自定义的高位端口，例如 22822
    - #Port 22
    + Port 22822

    # 禁止 root 用户通过 SSH 登录
    - #PermitRootLogin prohibit-password
    + PermitRootLogin no

    # 彻底禁用密码认证，只允许密钥认证
    - #PasswordAuthentication yes
    + PasswordAuthentication no

    # 确保公钥认证是开启的
    PubkeyAuthentication yes
    ```
3.  **在腾讯云控制台的防火墙页面**，执行以下操作：
    - **删除** 允许 `TCP:22` 端口的规则。
    - **新建** 一条规则，允许 `TCP:22822` (您设置的新端口) 的流量通过。
4.  **在服务器上**，重启 SSH 服务使配置生效：
    ```bash
    sudo systemctl restart ssh
    ```
    执行此命令后，您当前的 SSH 连接会立即断开。

#### **步骤 1.5: 部署主动入侵防御系统 (Fail2Ban)**
1.  **在本地 Mac 上**，使用新的端口号重新登录您的服务器：
    ```bash
    ssh -p 22822 myuser@[您的服务器公网IP]
    ```
2.  **在服务器上**，安装 `Fail2Ban`：
    ```bash
    sudo apt update && sudo apt install fail2ban -y
    ```
3.  为 `Fail2Ban` 创建一个本地配置文件，以避免在软件更新时配置被覆盖：
    ```bash
    sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
    ```
4.  编辑本地配置文件，启用对 SSH 的监控并告知其新端口：
    ```bash
    sudo nano /etc/fail2ban/jail.local
    ```
5.  找到 `[sshd]` 段落，修改如下：
    ```ini
    [sshd]
    enabled = true
    port    = 22822 
    # 其他默认设置（如封禁时间、重试次数）已足够安全，无需修改
    ```
6.  重启 `Fail2Ban` 服务使其加载新配置：
    ```bash
    sudo systemctl restart fail2ban
    ```
7.  **验证（可选）**：您可以运行 `sudo fail2ban-client status sshd` 查看 `Fail2Ban` 是否已成功启动并监控您的新 SSH 端口。

---

### **第二阶段：本地环境配置**

目标：配置浏览器，使其能在需要时通过我们的安全隧道访问网络，以完成认证。

1.  在您的 Chrome 或 Firefox 浏览器中，从官方商店安装 **Proxy SwitchyOmega** 插件。
2.  打开插件的选项页面。
3.  点击“新建情景模式...”，输入模式名称 `ssh-gemini`，类型选择“代理服务器”，然后创建。
4.  在该情景模式的设置中，进行如下配置：
    - **代理协议**: `SOCKS5`
    - **代理服务器**: `127.0.0.1`
    - **代理端口**: `1080`
5.  点击左侧的“应用选项”按钮保存配置。

---

### **第三阶段：日常使用工作流**

#### **首次启动与认证**
1.  **建立安全隧道**：在您的本地 Mac 终端，执行以下命令。它会在后台安静地启动一条加密隧道。
    ```bash
    ssh -p 22822 -D 1080 -f -C -q -N myuser@[您的服务器公网IP]
    ```
    > **命令解释**:
    > - `-p 22822`: 指定连接我们设置的非标准SSH端口。
    > - `-D 1080`: 在本地的 1080 端口创建一个SOCKS5动态代理。
    > - `-f`: 请求 ssh 在执行命令前转入后台。
    > - `-C`: 请求压缩所有数据。
    > - `-q`: 静默模式。
    > - `-N`: 不执行远程命令，仅用于端口转发。

2.  **配置终端代理**：**打开一个全新的终端窗口/标签页**，执行以下命令。此命令仅对当前终端会话有效。
    ```bash
    export ALL_PROXY=socks5://127.0.0.1:1080
    ```
3.  **运行 Gemini CLI**：在**同一个**设置了代理的窗口中，运行 `gemini-cli` 的安装与启动命令：
    ```bash
    npx https://github.com/google-gemini/gemini-cli
    ```
4.  `gemini-cli` 会在终端打印出一个 Google 认证的 URL，复制它。
5.  **浏览器认证**：
    - 点击浏览器右上角的 SwitchyOmega 插件图标，选择我们创建的 `ssh-gemini` 模式。
    - 在浏览器中粘贴并打开刚才复制的 URL，完成 Google 登录授权流程。
    - **重要**：授权成功后，立即将 SwitchyOmega 切换回“直接连接”模式，以免影响日常浏览。
6.  切换回终端，您会看到 `gemini-cli` 提示认证成功。

#### **后续日常使用**
认证是一次性的（除非凭据过期）。之后每次您想使用 `gemini` 时，流程将非常简单：
1.  确保 SSH 隧道正在运行（执行步骤 3.1 的命令）。
2.  打开新终端，设置代理（`export ALL_PROXY=...`）。
3.  直接在项目目录中运行 `gemini` 命令，例如 `gemini "帮我重构这个函数"`。

## 4. 优化手段：简化日常工作流

在您成功配置并使用主方案后，可以采用以下优化手段，将原本需要多个手动步骤的日常工作流，简化为“一键式”操作，极大提升使用效率。

### **优化步骤 1: 创建 SSH 连接别名 (简化基础)**

此步骤利用 SSH 客户端的配置文件 `~/.ssh/config`，为您的服务器连接创建一个简短、易记的别名，避免每次都输入长长的命令。

1.  在您的本地 Mac 终端，打开或创建 SSH 配置文件：
    ```bash
    nano ~/.ssh/config
    ```
2.  在文件中添加以下内容。这会为您服务器创建一个名为 `gemini-proxy` 的别名。请务必将 `HostName` 字段的 IP 地址替换为您自己的。
    ```ini
    # Gemini Proxy Server
    Host gemini-proxy
        HostName [您的服务器公网IP]
        User myuser
        Port 22822
        DynamicForward 1080
        RequestTTY no
        Compression yes
        LogLevel QUIET
        ExitOnForwardFailure yes
        ServerAliveInterval 60
    ```
3.  保存并退出 (`Ctrl+X`, `Y`, `Enter`)。
    从此，原来那条冗长的 `ssh -p 22822 -D 1080 ...` 命令，现在就可以通过 `ssh -f -N gemini-proxy` 来实现了。

### **优化步骤 2: 全流程自动化 (使用 Shell 函数)**

此步骤是实现“一键化”的核心。我们将在您的 Shell 配置文件 (`~/.zshrc` for Zsh) 中编写一小段函数，它会封装所有必要的逻辑，实现完全自动化的体验。

1.  打开您的 Zsh 配置文件：
    ```bash
    nano ~/.zshrc
    ```
2.  将以下整段代码粘贴到文件的**末尾**：
    ```zsh
    # --- Gemini CLI Automation Functions ---

    # 启动 Gemini 的 SSH 隧道
    # 它会检查隧道是否已运行，如果没运行则使用 ~/.ssh/config 中的 'gemini-proxy' 别名启动
    start_gemini_tunnel() {
        # 使用 lsof 检查本地 1080 端口是否被监听
        if ! lsof -i:1080 > /dev/null; then
            echo "Starting Gemini SSH tunnel..."
            ssh -f -N gemini-proxy
            sleep 2 # 短暂等待，确保隧道有足够时间建立
            if lsof -i:1080 > /dev/null; then
                echo "Tunnel started successfully."
            else
                echo "Error: Failed to start SSH tunnel." >&2
                return 1
            fi
        fi
    }

    # 封装 gemini 命令。当您在终端执行 'gemini' 时，这个函数会自动被调用
    gemini() {
        # 首先，确保隧道已启动，如果启动失败则中止后续操作
        start_gemini_tunnel || return
    
        # 使用代理环境变量来执行真正的 gemini 命令
        # "$@" 会将您输入的所有参数 (如 "重构这段代码") 原封不动地传递给 gemini-cli
        ALL_PROXY=socks5://127.0.0.1:1080 command gemini "$@"
    }

    # (可选) 提供一个手动停止隧道的命令，方便在一天工作结束后关闭后台隧道
    stop_gemini_tunnel() {
        echo "Stopping Gemini SSH tunnel..."
        local tunnel_pid=$(lsof -t -i:1080)
        if [ -n "$tunnel_pid" ]; then
            kill "$tunnel_pid"
            echo "Tunnel stopped."
        else
            echo "No active tunnel found on port 1080."
        fi
    }
    ```
3.  保存并退出 (`Ctrl+X`, `Y`, `Enter`)。
4.  让配置立即生效，执行：
    ```bash
    source ~/.zshrc
    ```

### **全新的工作流**

完成以上优化后，您的日常工作流程将变得极其简单：

1.  打开任何终端窗口。
2.  直接运行您的 `gemini` 命令，例如：
    ```bash
    gemini "请帮我解释一下 'gemini-cli-secure-deployment-plan.md' 中 Fail2Ban 的作用"
    ```

当您执行 `gemini` 时，我们编写的函数会自动在后台检查并启动SSH隧道，然后带上代理执行真正的 `gemini-cli` 程序。一切都已为您自动完成。

当您一天的工作结束，可以执行我们提供的可选命令来关闭后台的SSH隧道：
```bash
stop_gemini_tunnel
```

## 5. 备选思路：V2Ray/Xray “一劳永逸”方案简介

除了上述极致纯净的 SSH 方案外，还存在一种基于 V2Ray 或其上游核心 Xray 的现代化代理方案。

- **优势**:
    - **连接更稳定、性能更优**：这些工具专为代理场景设计，使用 VMess/VLESS 等协议，在复杂的网络环境下表现比 SSH 隧道更稳定、抗干扰能力更强。
    - **体验更无感**：通过在本地运行一个客户端程序，可以实现智能路由（例如，访问 Google 走代理，访问国内网站直连），做到“一次配置，永久无感”，无需频繁设置环境变量或切换浏览器插件。

- **基本原理**:
    1.  在服务器上安装 V2Ray/Xray 服务，并配置一个安全的协议（如 VLESS）和一个长而随机的 UUID 作为认证凭据。
    2.  在本地电脑上运行对应的客户端程序，并配置好服务器地址、端口和 UUID。
    3.  客户端会在本地创建一个 SOCKS5 或 HTTP 代理端口，并根据内置的规则列表智能分流网络请求。

- **为何未作主方案**:
    此方案的主要权衡点在于，它**必须在本地运行一个客户端程序**（无论是图形化界面的 `.app` 还是命令行的核心二进制文件），这与本项目“本地零安装”的核心约束相悖。但如果未来对连接稳定性的要求高于对环境纯净度的要求，这是一个非常值得考虑的升级路径。 