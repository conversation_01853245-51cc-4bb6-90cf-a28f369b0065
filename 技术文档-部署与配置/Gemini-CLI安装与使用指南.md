# Gemini-CLI 安装与使用终极指南：从云服务器到本地环境

本指南旨在为用户提供一份详尽、清晰、具有高度实践指导意义的 `gemini-cli` 安装与配置手册。我们将针对两种最主流的使用场景，提供分步式的、端到端的解决方案。

-   **第一部分：云服务器篇** - 面向在拥有直接国际互联网访问权限的海外云服务器（如 Ubuntu, CentOS）上进行部署的用户。
-   **第二部分：本地环境篇** - 面向在需要网络代理的本地开发环境（以 macOS 为例）上进行安装和使用的用户。

无论您是哪种情况，本指南都将帮助您顺利完成配置，并高效地在终端中使用 Gemini 的强大功能。

**官方项目地址：** [https://github.com/google-gemini/gemini-cli](https://github.com/google-gemini/gemini-cli)

---

## 第一部分：云服务器篇 (直接网络访问环境)

本部分专门讲解如何在可以直接访问 Google 服务的海外云服务器上，安装和配置 `gemini-cli`。

### 1. 前提条件

-   **操作系统：** 任意现代 Linux 发行版。
-   **软件依赖：** 已安装 **Node.js v20 或更高版本**。

### 2. 标准安装与配置流程 (SOP)

#### 第1步：检查 Node.js 版本

首先，通过 SSH 登录到您的服务器，并执行以下命令来验证 Node.js 的版本。

```bash
node -v
```

您应该会看到类似 `v20.11.0` 或更高的版本号。如果版本过低或未安装，请先通过 `nvm` (Node Version Manager) 或其他包管理器进行安装或升级。

#### 第2步：全局安装 gemini-cli

我们使用 Node.js 的包管理器 `npm` 来全局安装 `gemini-cli`。

```bash
npm install -g @google/gemini-cli
```

> **提示：** 根据您的 Node.js 安装方式和用户权限，您可能需要使用 `sudo` 来执行此命令：`sudo npm install -g @google/gemini-cli`。

安装成功后，`gemini` 命令将在您系统的任何路径下都可使用。

#### 第3步：进行个人账户认证（关键步骤）

这是在没有图形界面的服务器环境（Headless）下进行认证的核心流程。它通过一个安全的 OAuth 2.0 机制将您的终端与 Google 账户关联起来。

1.  在您的服务器终端中，直接运行 `gemini` 命令启动程序：
    ```bash
    gemini
    ```

2.  程序首次运行时，会提示您进行认证。您会看到一段提示文字，以及一个需要您在本地浏览器中打开的 URL。
    ```
    To continue, you must log in to Google.
    Your browser will be opened to the following URL:
    https://accounts.google.com/o/oauth2/v2/auth?....
    ```

3.  **在您的本地电脑上操作：**
    a. **完整地复制**服务器终端中显示的那个以 `https://accounts.google.com/...` 开头的 URL。
    b. 打开您**本地电脑**上的任意网页浏览器（如 Chrome, Firefox, Safari）。
    c. 将复制的 URL **粘贴**到浏览器的地址栏并访问。

4.  浏览器会加载一个标准的 Google 登录和授权页面。请按照提示：
    a. 登录您的个人 Google 账户。
    b. 点击“允许”或“Allow”，授予 Gemini CLI 访问权限。

5.  授权成功后，浏览器页面会显示一个**授权码 (Authorization Code)**。它看起来像一长串随机字符。

6.  **回到您的服务器终端：**
    a. **复制**浏览器中显示的那个授权码。
    b. 将其**粘贴**到服务器终端正在等待输入的提示符后面。
    c. 按下 `Enter` 键。

#### 第4步：验证安装

如果授权码正确，`gemini-cli` 会完成认证并显示欢迎信息，接着您会看到一个 `>` 提示符，表示它正在等待您的指令。至此，您已成功完成所有配置。

您可以输入一个简单的问题来测试它是否正常工作：
```
> Describe what you are in one sentence.
```
如果它返回了 Gemini 的回答，那么一切就绪。

### 附录：解决云服务器认证难题（SSH 端口转发）

在某些网络环境或服务器配置下，您可能会遇到一个棘手的问题：标准的认证流程失败。具体表现为：

*   在服务器上运行 `gemini` 后，它卡在 `Waiting for auth...` 并且不显示认证URL。
*   或者，即使您通过 `gemini --debug` 获取了URL，在本地浏览器完成授权后，流程最终失败，因为浏览器试图访问一个 `http://localhost:[PORT]` 地址。

这个问题的**根本原因**是：`gemini-cli` 在服务器上启动了一个微型网页服务来监听Google的认证回调。这个服务监听的是服务器的 `localhost` (即 `127.0.0.1`) 上的一个**动态端口**。当你在本地浏览器完成授权后，Google会指示你的浏览器跳转回 `http://localhost:[PORT]`。这里的 `localhost` 指的是**你的本地电脑**，而不是远程服务器。因此，回调请求无法到达正在服务器上等待的 `gemini-cli` 进程，导致认证超时失败。

**解决方案**是利用 SSH 创建一个安全的“隧道”，将你本地电脑上对特定端口的访问请求，“转发”到远程服务器的同一个端口上。

**详细操作步骤 (SOP):**

1.  **获取动态端口号 (在服务器上操作)**
    *   通过 SSH 登录到您的服务器。
    *   运行 `gemini --debug` 命令。这个调试模式会输出更详细的信息。
    *   在输出信息中，找到那串长长的认证URL。请**完整复制**这串URL备用。
    *   同时，在 `debug` 信息中找到类似 `Listening on http://localhost:XXXXX` 的行，或在认证URL参数中找到 `redirect_uri=http%3A%2F%2Flocalhost%3A**XXXXX**` 的部分。记下这个 **XXXXX** 的端口号（例如 `43281`）。
    *   **保持这个服务器终端窗口开启**，不要关闭，因为它正在等待认证回调。

2.  **建立SSH隧道 (在本地电脑上操作)**
    *   **不要关闭**之前的服务器终端。在您的**本地电脑**上打开一个**新的终端窗口**。
    *   执行以下 `ssh -L` 命令来建立隧道。命令的格式为 `ssh -L [本地端口]:localhost:[远程端口] user@your_server_ip`。
    *   **关键点**：这里的 `[本地端口]` 和 `[远程端口]` 都必须是您在上一步中记下的那个**动态端口号**。
    *   例如，如果端口号是 `43281`，命令就是：
        ```bash
        ssh -L 43281:localhost:43281 your_username@your_server_ip
        ```
    *   输入服务器密码或使用密钥进行登录。成功后，**让这个SSH隧道终端也保持运行**。

3.  **完成浏览器认证 (在本地电脑上操作)**
    *   现在，回到您的本地电脑，打开网页浏览器。
    *   将第一步中从服务器复制的**完整认证URL**粘贴到地址栏并访问。
    *   在打开的Google页面中，正常登录您的账户并授予权限。

4.  **验证成功**
    *   授权成功后，Google会尝试将您的浏览器重定向到 `http://localhost:43281` (使用您的实际端口号)。
    *   由于SSH隧道的存在，这个请求会被您的本地电脑拦截，并通过隧道安全地转发到您服务器的 `localhost:43281`。
    *   服务器上正在运行的 `gemini-cli` 进程会成功接收到这个回调请求，完成认证。
    *   此时，切换回您**第一个服务器终端窗口**，您应该会看到认证成功的消息，并出现 `>` 提示符。现在您可以关闭那个用于隧道的第二个本地终端了。

---

## 第二部分：本地环境篇 (macOS + 代理环境)

本部分专门讲解如何在需要网络代理的本地 macOS 环境中，优雅、无感地使用 `gemini-cli`。

### 1. 前提条件 - 网络代理

-   **核心要求**：您必须已在本地电脑上安装并配置好一个能正常工作的网络代理工具。
-   **推荐工具**：强烈推荐使用 **`Clash Verge Rev`** 或其他支持 **TUN (虚拟网卡) 模式** 的现代代理客户端。

#### **为什么强烈推荐 TUN 模式？**
`gemini-cli` 是一个终端工具，默认情况下它不会像浏览器一样自动使用系统代理。传统的解决方法是为终端手动设置 `ALL_PROXY` 环境变量，非常繁琐。

**TUN 模式** 则从根本上解决了这个问题。它能在系统网络层强制接管所有应用的流量（包括终端），实现真正的“一键代理，全局无感”。**本指南将以开启 TUN 模式作为标准操作流程。**

### 2. 本地化安装与配置流程 (SOP)

#### 第1步：开启代理工具的 TUN 模式

在开始安装前，请先打开您的 `Clash Verge Rev`，并在其“首页 (Dashboard)”中，**开启“虚拟网卡模式 (TUN Mode)”**。

#### 第2步：通过 Homebrew 安装 gemini-cli

在 macOS 上，使用 `Homebrew` 是管理命令行工具的最佳实践。打开“终端”应用，运行以下命令：

```bash
brew install gemini-cli
```
Homebrew 会自动处理好依赖和路径问题。

#### 第3步：进行个人账户认证（桌面环境）

在开启了 TUN 模式的前提下，本地认证流程会比服务器环境简单得多。

1.  确保您的 TUN 模式已开启。
2.  在终端中，直接运行 `gemini` 命令：
    ```bash
    gemini
    ```

    > #### 首次运行的权限提示 (重要)
    >
    > 当您首次执行此命令时，macOS 系统很可能会弹出一个权限请求窗口，提示 **“‘终端’想访问‘桌面’（或其它）文件夹中的文件。”**
    >
    > **请放心，这是一个完全正常且预期的 macOS 安全提示。**
    >
    > *   **原因**: `gemini-cli` 作为一个强大的本地工具，需要权限来读取您的项目文件、配置文件等。它的启动行为会触发 macOS 的隐私保护机制。从系统层面看，是“终端”这个应用在为 `gemini-cli` 请求权限。
    > *   **操作**: 为了让 `gemini-cli` 正常工作，**请务必点击“允许”**。如果后续系统继续询问访问“文档”、“下载”等文件夹的权限，也请一并允许。

3.  `gemini-cli` 会检测到桌面环境，并**自动打开**您的默认浏览器，并访问 Google 授权页面。
4.  在浏览器中登录您的 Google 账户，并点击“允许”或“Allow”。
5.  授权成功后，您只需关闭浏览器窗口即可。`gemini-cli` 会在后台自动完成认证。

#### 第4步：验证与日常使用

认证成功后，终端中的 `gemini` 会显示 `>` 提示符。您可以直接开始提问，无需任何额外操作。

由于 TUN 模式的存在，您在任何时候想使用 `gemini`，只需**确保 TUN 模式是开启的**，然后直接在终端输入 `gemini` 即可，无需添加任何代理参数。

---

## 第三部分：通用技巧与附录

### 1. 版本管理 (检查、确认与更新)

由于您在本地和云服务器上均使用 `npm` 进行安装，因此版本管理的命令和逻辑在这两个环境中完全一致。

#### 检查当前已安装的版本
您有两种方式可以查看：
- **方式一 (通过工具本身):** 这是最快捷的方法。
  ```bash
  gemini --version
  ```
- **方式二 (通过 npm):** 这个方法能看到更详细的包信息。
  ```bash
  npm list -g @google/gemini-cli
  ```

#### 确认官方发布的最新版本
在更新前，最好先确认一下最新版本号是多少。
- **最权威的方式：访问 GitHub Releases 页面**
  官方的发布页面 [https://github.com/google-gemini/gemini-cli/releases](https://github.com/google-gemini/gemini-cli/releases) 列出了所有稳定版的更新日志和版本号。
- **最快捷的方式：通过 npm 查询**
  ```bash
  npm view @google/gemini-cli version
  ```

#### 更新到最新版本
当您确认有新版本发布后，使用 `npm` 的 `update` 命令即可。
```bash
npm update -g @google/gemini-cli
```
> **提示**：根据您的 Node.js 安装方式和用户权限，可能需要使用 `sudo` 来执行此命令。

### 2. 卸载工具

- **通过 npm 卸载:**
  ```bash
  npm uninstall -g @google/gemini-cli
  ```
  (同样，可能需要 `sudo`)

### 3. 备用方案：使用 API 密钥

如果您不想使用个人账户登录，或者需要在自动化脚本中使用，可以采用 API 密钥的方式。

1.  从 [Google AI Studio](https://aistudio.google.com/app/apikey) 创建一个 API 密钥。
2.  在您的服务器或本地终端设置环境变量：
    ```bash
    export GEMINI_API_KEY="在这里粘贴您的API密钥"
    ```
3.  为了让它永久生效，可以将其添加到 shell 的配置文件中（如 `~/.bashrc` 或 `~/.zshrc`）：
    ```bash
    echo 'export GEMINI_API_KEY="在这里粘贴您的API密钥"' >> ~/.zshrc
    source ~/.zshrc
    ```

### 4. 高级技巧：使用多账户分离配额

您可以利用 `gemini-cli` 在不同环境中独立认证的特性，为服务器和本地电脑分别绑定不同的 Google 账户，从而有效利用两个账户各自的免费 API 配额。

`gemini-cli` 的认证凭据存储在每个操作系统环境的用户主目录中（`~/.gemini/`），因此服务器和您本地电脑的认证信息是完全隔离的。只需在不同设备上登录不同的 Google 账户进行认证即可。 

---

## 第四部分：日常使用技巧与核心应用场景

安装和基础认证只是起点，`gemini-cli` 的真正威力在于其深度集成的交互能力。本章将首先为您描绘其核心能力的蓝图，然后通过几个典型的开发者日常工作场景，展示如何活用这些功能，将它从一个问答工具变为一个真正的编程伙伴。

### 核心能力概览：它到底能做什么？

`Gemini CLI` 的本质是**一个深度集成在您本地开发环境（终端/命令行）中的 AI 助手**。它与网页版AI工具的根本区别在于，它能直接**看到并操作**您电脑上的文件、代码库和各种开发工具，就像一个坐在您旁边的“结对编程伙伴”。

#### 能力一：代码库的“智能导航员”与“重构助手”

> **官方描述**: `Query and edit large codebases in and beyond Gemini's 1M token context window.`

这意味着 `Gemini CLI` 不受传统AI模型“上下文长度”的限制，有能力理解整个大型项目的全貌。

-   **应用场景**:
    -   **接手老项目**: 直接在项目根目录提问：“这个项目的核心架构是怎样的？” 或 “登录功能的代码在哪里实现的？”
    -   **代码重构与Bug修复**: 让它帮你优化某个函数，或根据一个 GitHub Issue 来草拟修复方案。

#### 能力二：从“想法”到“代码”的“创造者”

> **官方描述**: `Generate new apps from PDFs or sketches, using Gemini's multimodal capabilities.`

`Gemini CLI` 不仅能处理文本，还能“看懂”图片和文档（即**多模态能力**）。

-   **应用场景**:
    -   **原型快速实现**: 将产品经理给的需求文档（PDF）或应用界面手绘草图“喂”给它，让它直接生成初步的项目框架或前端代码。

#### 能力三：繁琐任务的“自动化机器人”

> **官方描述**: `Automate operational tasks, like querying pull requests or handling complex rebases.`

它可以帮你处理日常开发中那些重复、繁琐但又必须做的事情。

-   **应用场景**:
    -   **项目管理**: “给我一份昨天所有代码提交的总结，按功能模块分类。”
    -   **Git 助手**: 在被复杂的 `git rebase` 命令困扰时，向它求助，让它一步步指导你。
    -   **脚本生成**: 让它为你生成批量处理文件（如图片格式转换）的 Shell 脚本。

#### 能力四：连接万物的“超级工具箱”

> **官方描述**: `Use tools and MCP servers to connect new capabilities...` & `Ground your queries with the Google Search tool...`

`Gemini CLI` 的能力是**可以无限扩展的**。它能像指挥官一样，调用外部的各种工具来完成任务。

-   **应用场景**:
    -   **实时信息查询**: 它连接的后端模型具备**搜索感知**，在回答需要最新信息的问题时，会自动利用Google搜索来确保答案的准确性。
    -   **多媒体生成与外部工具调用**: 通过MCP插件系统，它可以连接并调用 `Imagen` (文生图) 或 `Veo` (文生视频) 等工具，或者连接到你公司的Jira、内部知识库等。

---

接下来，我们将通过具体的操作命令，详细展示如何在实践中应用这些强大的能力。

### 1. 场景一：快速理解一个陌生项目

当你接手一个不熟悉的代码库时，`gemini-cli` 可以帮你快速建立认知。

**步骤1: 导入代码上下文进行提问**
无需手动复制粘贴代码。使用 `@` 符号可以直接将文件或整个目录作为上下文提供给 Gemini。

-   **导入单个文件**：想知道项目的入口文件是做什么的？
    ```
    > @src/main.js What is the main purpose of this file?
    ```
-   **导入整个目录**：想了解某个组件文件夹的整体功能？
    ```
    > @src/components/ Summarize the role of each component in this directory.
    ```
    > **提示**: Gemini 会智能地忽略 `.git`、`node_modules` 以及 `.gitignore` 中匹配的大型或二进制文件，以优化性能。

**步骤2: 直接与 Shell 交互**
在分析代码时，你可能想随时查看文件系统结构，无需退出 Gemini 环境。

```
> !ls -R
```
这个命令会直接在当前会话中列出所有文件和目录，结果也会成为对话上下文的一部分。

**步骤3: 保存与恢复分析会话**
代码分析可能需要多次进行。你可以随时保存当前的对话，以便之后回到完全相同的上下文。

-   **保存会话**:
    ```
    > /chat save project-analysis
    ```
-   **恢复会话**:
    ```
    > /chat resume project-analysis
    ```

### 2. 场景二：辅助编码与重构

`gemini-cli` 不仅能读懂代码，还能帮你编写和修改代码。

**步骤1: 注入“长期记忆”**
在开始一个项目时，可以把通用的规范和要求“喂”给 Gemini，让它在整个会话中都记住这些规则。

```
> /memory add "Our project's coding style follows the Airbnb JavaScript Style Guide."
> /memory add "All commit messages must follow the conventional commits specification."
```
你可以用 `/memory show` 来查看当前所有生效的记忆。

**步骤2: 生成符合规范的代码**
现在，当你提出编码请求时，Gemini 会遵循你之前设定的“记忆”。

```
> Write a React functional component that fetches and displays a list of users.
```
生成的代码会更贴近你的项目规范。

**步骤3: 与版本控制无缝衔接**
在 AI 建议修改后，可以直接在 `gemini-cli` 中检查代码状态。

```
> !git status
> !git diff
```

### 3. 场景三：结合 Shell 命令实现简单自动化

你可以将 `gemini-cli` 的自然语言理解能力和 Shell 的强大功能结合，创造简单的工作流。

**需求**: 将项目中所有 `.jpeg` 格式的图片批量转换为 `.png`。

你可以这样提问：
```
> I need to convert all jpeg files in the current directory and its subdirectories to png. Please generate a shell command for me.
```
Gemini 可能会返回一个基于 `find` 和 `imagemagick` 的命令，你可以直接复制使用，甚至让它解释这个命令的每一个部分。

### 4. 核心命令速查表

为了方便快速查找，这里总结了 `gemini-cli` 最常用的一些内置命令。

| 命令                 | 功能                 | 说明                                                               |
| -------------------- | -------------------- | ------------------------------------------------------------------ |
| `/chat save <tag>`   | 保存对话             | 为当前会话创建一个名为 `<tag>` 的快照，以便后续恢复。              |
| `/chat resume <tag>` | 恢复对话             | 加载之前保存的会话，恢复所有上下文和记忆。                         |
| `/chat list`         | 列出所有已存对话     | 显示所有已保存的对话标签。                                         |
| `/memory add "..."`  | 添加长期记忆         | 让 Gemini 在当前会话中始终记住一条信息。                           |
| `/memory show`       | 显示所有记忆         | 查看当前所有已添加的长期记忆。                                     |
| `/compress`          | 压缩对话历史         | 总结当前对话，减少 token 消耗，同时保留关键上下文。                |
| `@<path>`            | 导入文件/目录上下文  | 将指定路径的文件或目录内容作为本次提问的上下文。                   |
| `!<command>`         | 执行 Shell 命令      | 在不离开 Gemini 的情况下执行任意终端命令。                         |
| `/clear`             | 清空屏幕             | 清除终端显示内容 (快捷键: `Ctrl + L`)。                            |
| `/help` 或 `/?`      | 显示帮助信息         | 列出所有可用的斜杠命令。                                           |
| `/stats`             | 查看使用统计         | 显示详细的用量统计。包括本次会话的token消耗、上下文窗口占用率，以及您账户的API请求配额（如：60次/分钟，1000次/天）的使用情况。|
| `/exit` 或 `/quit`   | 退出程序             | 关闭 `gemini-cli`。                                                | 

---

## 第五部分：双剑合璧——终极协同开发工作流

本章节旨在为您提供一套极其强大的协同开发工作流，将 `Gemini Code Assist` (IDE插件) 和 `gemini-cli` (命令行) 的优势发挥到极致。

**核心理念**:
- **`gemini-cli` (命令行)**: 是您的**项目架构师**和**自动化工程师**，负责宏观规划、全局分析和批量操作。
- **`Gemini Code Assist` (IDE插件)**: 是您的**结对编程伙伴**，负责微观实现、实时辅助和精准编码。

---

### **场景一：从零开始一个【新项目】**

#### **阶段 1：概念构思与技术选型 (主角: `gemini-cli`)**

您只有一个模糊的想法或一份草稿。`gemini-cli` 可以帮您将想法具象化。

1.  **澄清想法，建议技术栈**：
    在一个空文件夹中启动 `gemini`。
    > "我准备开发一个个人博客系统。用户可以发布文章，可以对文章进行评论。请为我推荐一个现代化的、适合个人项目的前后端技术栈，并解释原因。"

2.  **生成项目脚手架**：
    根据上一步的建议，让 `gemini-cli` 为您创建基础的项目结构和配置文件。
    > "好的，我决定使用 Next.js + Tailwind CSS + Supabase。请为我生成这个项目的推荐目录结构，并为我创建 `package.json` 和 `tsconfig.json` 的初始版本。"

    `gemini-cli` 会直接帮您创建这些文件和目录，完成项目的冷启动。

#### **阶段 2：核心功能编码 (主角: `Gemini Code Assist`)**

项目骨架搭好了，现在进入 VS Code 进行核心编码。

1.  **生成页面和组件框架**：
    在 VS Code 中，打开一个新文件，如 `src/app/page.tsx`。使用侧边栏聊天或内联聊天 (`Cmd/Ctrl+I`)：
    > "Create a basic homepage layout using Next.js and Tailwind CSS. It should include a header with the blog title, a main content area for posts, and a footer."

2.  **实现具体逻辑**：
    当您需要编写一个函数时，例如从 Supabase 获取文章列表，`Code Assist` 的**代码补全** (`Tab`键接受) 会极大地加速您的开发。您也可以直接命令它：
    > "Write a function `fetchPosts` that uses the Supabase client to fetch the 10 most recent posts from the 'posts' table."

3.  **快速调试与解释**：
    遇到一个不理解的API用法或代码片段？选中它，右键点击 -> "Gemini: Explain This"，无需离开编辑器就能获得解释。

#### **阶段 3：功能迭代与规范统一 (主角: `gemini-cli` + `Code Assist`)**

您完成了核心功能，现在要添加“评论”功能。

1.  **规划新功能模块 (宏观 - `gemini-cli`)**：
    回到终端，让 `gemini-cli` 帮您思考如何以最规范的方式集成新功能。
    > "@. 我要为博客添加评论功能。请分析现有项目结构，并建议我应该在哪些目录下创建哪些新文件（例如API路由、数据库交互、前端组件），并给出每个文件的大致功能轮廓。"

2.  **编码新模块 (微观 - `Code Assist`)**：
    带着 `gemini-cli` 给出的清晰蓝图，回到 VS Code，使用 `Code Assist` 逐一实现评论组件、API接口等。

3.  **确保代码风格统一 (宏观 - `gemini-cli`)**：
    新代码写完后，让 `gemini-cli` 帮您做一次代码审查。
    > "@src/app/posts/[id]/comments/ 请检查这个目录下的代码是否遵循了我们项目的一贯编码风格和最佳实践。"

#### **阶段 4：文档、测试与收尾 (主角: `gemini-cli`)**

项目即将上线，`gemini-cli` 是最好的收尾工具。

1.  **生成测试用例**：
    > "@src/lib/api.ts 为这个文件中的所有函数生成单元测试用例，使用 Jest 和 React Testing Library。"

2.  **生成项目文档**：
    > "@. 请为这个博客项目生成一份完整的 README.md 文件，包括项目介绍、技术栈、本地启动步骤以及部署指南。"

---

### **场景二：维护和迭代一个【已有项目】**

#### **阶段 1：项目上手与代码理解 (主角: `gemini-cli`)**

您刚刚 `git clone` 了一个庞大而陌生的代码库。

1.  **获取高层架构图景**：
    在项目根目录启动 `gemini`。
    > "@. 这是我第一次接触这个项目。请为我描述它的核心架构，主要的业务模块有哪些，以及数据是如何在前后端之间流转的？"

2.  **追踪核心业务逻辑**：
    针对某个具体功能，进行深度钻取。
    > "@. 请追踪用户“下订单”这个功能的完整代码路径。从用户点击“提交订单”按钮开始，涉及到了哪些前端组件、API路由、后端服务和数据库操作？请列出关键文件和函数。"

3.  **理解特定模块**：
    > "@src/payment/engine/ 请详细解释这个目录的核心职责是什么？它依赖了哪些外部服务？"

#### **阶段 2：Bug 修复 (主角: `gemini-cli` + `Code Assist`)**

您接手了一个 Bug Ticket。

1.  **定位问题代码 (宏观 - `gemini-cli`)**：
    > "@. Ticket #789 指出，在特定条件下计算折扣时会出现精度问题。项目中处理价格和折扣计算的逻辑主要在哪些文件里？"

2.  **理解并修复 Bug (微观 - `Code Assist`)**：
    `gemini-cli` 会告诉您问题可能在 `src/utils/pricing.ts`。您在 VS Code 中打开它，选中可疑函数，右键 -> "Gemini: Explain This" 来快速理解逻辑。然后，您可以通过内联聊天修复它：
    > "This function has precision issues when handling decimals. Please refactor it to use a library like `Decimal.js` to ensure accuracy."

#### **阶段 3：开发新功能 (主角: `gemini-cli` + `Code Assist`)**

您需要在这个庞大的项目中添加一个与现有功能类似的新功能。

1.  **学习现有模式 (宏观 - `gemini-cli`)**：
    模仿是最高效的开发。
    > "@. 我需要添加一个新的'产品类别'管理功能。请分析项目中已有的'产品管理'功能是如何实现的，并为我提供一份添加'类别管理'功能的步骤清单，确保与现有代码风格和架构模式保持一致。"

2.  **编码新功能 (微观 - `Code Assist`)**：
    遵循 `gemini-cli` 提供的“模仿指南”，在 VS Code 中使用 `Code Assist` 快速创建和编写新文件。

#### **阶段 4：代码审查与提交 (主角: `gemini-cli`)**

您的代码已完成，准备提交 Pull Request。

1.  **自我审查**：
    > "@. 请帮我审查我刚才修改过的文件，检查是否存在潜在的bug、性能问题或不符合项目规范的地方。" (`@` 后面可以跟具体文件名)

2.  **生成提交信息和 PR 描述**：
    这是 `gemini-cli` 的拿手好戏。
    > "!git diff --staged. Based on these changes, please write a commit message following the Conventional Commits specification, and then generate a detailed Pull Request description in Markdown format, explaining what I did and why."

---

## 第六部分：高级功能详解 (对话、规则与插件)

本部分将深入探讨 `gemini-cli` 的三大高级功能，帮助你将它打造成更符合个人工作流的强大工具。

### 1. 理解对话核心：上下文窗口 (Context Window)

在深入了解具体命令前，必须先理解 `gemini-cli` 对话的基石——**上下文窗口 (Context Window)**。您在状态栏看到的 `(XX% context left)` 正是它的指示器。

您可以将“上下文窗口”想象成 AI 在与您对话时，面前立着的一块**巨大但容量有限的“短期记忆白板”**。

-   **白板如何工作**：您与`gemini-cli`的每一次交互——您的问题、它的回答、您用`@`加载的文件、用`/memory`添加的记忆——都会被实时地**写在这块白板上**。AI 在回应您时，会回顾白板上的所有内容来确保对话的连贯性。
-   **`XX% context left` 的含义**：这个百分比非常直观，它表示“**当前这块白板还剩下 XX% 的空白空间**”。数字越高，代表AI的“短期记忆”越充裕。
-   **当白板写满时**：当这个百分比接近`0%`时，为了给新内容腾出空间，AI会开始“遗忘”**最早的对话**（就像从白板顶部开始擦除内容）。此时，它可能会失去对对话早期细节的记忆。
-   **应对策略 (`/compress`)**：`gemini-cli` 提供了聪明的 `/compress` 命令来解决这个问题。它会智能地将白板上的详细对话**总结为精炼的要点**，然后用这些要点替换掉原文，从而释放大量空间（`context left`百分比会回升），同时保留核心记忆。

理解上下文窗口，是高效管理长对话、处理复杂任务的关键。

### 2. 对话管理：保存与恢复会话快照

`gemini-cli` 的对话管理是基于**快照（Snapshot）**模式的，它允许你完整地保存和恢复一个会话，但不支持在当前会话中直接“关联”或“引用”另一个已保存的会话。

-   **保存当前会话: `/chat save <tag>`**
    这个命令会把当前对话的所有内容——包括你提出的问题、AI的回答、以及你用 `/memory add` 添加的临时记忆——打包成一个快照，并用你指定的 `<tag>` (标签) 来命名。
    ```bash
    # 假设你正在进行代码分析，并想保存进度
    > /chat save refactor-plan-v1
    ```

-   **恢复历史会话: `/chat resume <tag>`**
    这个命令会**丢弃当前正在进行的对话**，然后完整地加载你指定的 `<tag>` 所对应的那个历史快照。你会回到那个会話被保存时的确切状态，所有上下文和记忆都将被恢复。
    ```bash
    # 开始一个新会话后，想回到之前的重构计划
    > /chat resume refactor-plan-v1
    ```

-   **查看所有已存会话: `/chat list`**
    如果你忘记了保存过哪些标签，可以使用此命令列出所有可用的会话快照。

**核心概念总结**：你不能把两个会话合并。你只能在不同的会话快照之间进行“时空穿越”，即**从一个会话切换到另一个**。

#### 重要提示：对话历史是易失的 (Volatile)

一个非常关键的点是：**`gemini-cli` 不会自动保存你的对话历史**。

当你关闭终端窗口或退出 `gemini` 进程时，如果**没有**使用 `/chat save` 命令，当前会话的所有上下文（包括问题、回答和临时记忆）都会被**永久丢弃**。这和我们常用的即时通讯软件不同，它的会话存在于当前运行的进程内存中。

**最佳实践工作流 (防丢失SOP):**

1.  **结束会话前 (Save)**：在完成一项重要任务或准备关闭终端时，养成随手保存的习惯。
    ```bash
    > /chat save my-debug-session-for-feature-x
    ```

2.  **开启新会话后 (List & Resume)**：在新的终端里启动 `gemini` 后，你可以：
    ```bash
    # 1. 查看所有保存过的快照
    > /chat list
    
    # 2. 恢复到上次的工作状态
    > /chat resume my-debug-session-for-feature-x
    ```

遵循此工作流，可以确保你的重要工作不会因意外关闭而丢失。

#### 如何删除对话快照？

`gemini-cli` 目前没有提供内置的删除命令，你需要通过手动删除文件来完成。

1.  **定位快照目录**：打开终端，导航到 `~/.gemini/chats/` 目录。
2.  **找到快照文件**：该目录下的 `.json` 文件即为你的快照，文件名就是你保存时使用的标签。
3.  **删除文件**：使用 `rm <文件名>.json` 命令将其删除。
    > **警告**：这是一个永久性的物理删除操作，无法撤销。

### 3. 设置持久化规则：`gemini.md` 的妙用

`gemini-cli` 提供了两种机制来实现规则设置，一种是临时的（会话级），另一种是持久的（项目级），后者与 Cursor 的 Rules 功能非常相似。

-   **方式一：会话级临时规则 (`/memory add`)**
    使用 `/memory add` 命令添加的规则只会对**当前会话**生效。一旦你退出或切换会话，这些记忆就会丢失。这适用于临时的、一次性的指令。
    ```bash
    # 让AI在当前对话中始终用中文回答
    > /memory add "Please answer all my questions in Chinese."
    ```

-   **方式二：项目级持久化规则 (`gemini.md` 文件)**
    这是实现类似 Cursor "Rules" 的**最佳方式**。`gemini-cli` 会自动识别并加载项目根目录下的 `gemini.md` 文件作为持久化规则。

    **操作步骤：**
    1.  在你的项目文件夹根部，创建一个名为 `gemini.md` 的文件。
    2.  打开这个文件，将你的每一条规则写成一行。例如：

        ```markdown
        # gemini.md
        
        Always reply in Chinese.
        The project uses React with TypeScript.
        All code snippets must include JSDoc comments.
        Avoid using default exports.
        ```
    3.  保存文件。
    
    现在，只要你在**这个项目目录或其子目录**中运行 `gemini`，它就会自动加载 `gemini.md` 里的所有规则作为它的“长期记忆”。你同样可以用 `/memory show` 命令来验证这些项目级规则是否已成功加载。

### 4. 使用插件 (MCP)：探索与调用

#### `gemini-cli` 自带网络搜索吗？

这是一个需要澄清的重要概念：

*   **隐式搜索**：`gemini-cli` 连接的后端大模型（如Gemini 2.5 Pro）本身是**具备搜索感知**的。这意味着模型在回答需要最新信息的问题时，可能会在后台**自动**利用Google搜索来保证答案的准确性和时效性。这是一个你无法直接控制的**模型级能力**。
*   **显式搜索**：`gemini-cli` **没有内置**一个可以让你直接调用的“网络搜索”工具（如 `/search` 命令）。要实现这种可由用户指令触发的搜索，你必须通过MCP插件系统，连接到一个提供了该功能的外部服务器。

简单来说，它的“大脑”是联网的，但你必须通过插件为它的“双手”安装一个名为“搜索”的工具。

MCP (Model Context Protocols) 是 `gemini-cli` 的高级扩展系统，可以把它理解为一个**插件协议**。它允许 `gemini-cli` 连接到外部的工具或服务（如API、数据库、搜索引擎等），并在对话中调用它们。

作为普通用户，你可以通过以下步骤来探索和使用**已经连接好**的 MCP 工具：

1.  **第一步：列出可用工具 (`/mcp`)**
    这是探索 MCP 功能的入口。在 `gemini-cli` 中运行此命令，它会列出当前环境所有已注册并可用的 MCP 工具/服务。
    ```bash
    > /mcp
    ```

2.  **第二步：在提问中尝试调用**
    一旦你知道了工具的名称，就可以在提问时尝试通过自然语言来调用它。`gemini-cli` 会理解你的意图，并去执行相应的工具。
    ```bash
    # 假设上一步列出了一个名为 "web_search" 的工具
    > Use the 'web_search' tool to find the latest news about Gemini 2.5 Pro.
    ```

---

## 第六部分：开发者指南：添加MCP工具（插件）

本部分面向希望进一步扩展 `gemini-cli` 功能的开发者。

### 1. 核心概念：客户端 vs 服务器

首先，需要澄清一个核心概念：为 `gemini-cli`“添加”一个工具，并不是自己从零开始编写代码，而是**将 `gemini-cli` 连接到一个已经开发好的、独立运行的“MCP服务器”**。您可以把这些服务器看作是提供各种能力的“应用商店”，`gemini-cli` 通过连接它们来获得新功能。

这个过程分为两步：**启动服务器**和**配置客户端**。

### 2. 实例：连接一个社区开发的MCP服务器

我们将以一个由社区开发的、名为 `mcp-server-gemini` 的项目为例。这个服务器能为 `gemini-cli` 提供调用 Google API 的能力。

**第1步：获取并运行 MCP 服务器**

由于 `gemini-cli` 官方并未提供 MCP 服务器，我们需要依赖社区项目。一个简单的方式是使用 `npx` 直接运行托管在 GitHub 上的服务器代码。

打开一个**新的终端窗口**（不要在 `gemini-cli` 内部运行），然后执行以下命令：

```bash
# 确保你已经设置了 GEMINI_API_KEY 环境变量
export GEMINI_API_KEY="你的Google AI Studio API密钥"

# 使用 npx 启动服务器
npx -y github:aliargun/mcp-server-gemini
```

> **说明**：
> *   `npx` 是一个 Node.js 工具，可以临时下载并运行一个软件包，而无需永久安装。
> *   这个命令会从 GitHub 下载 `aliargun/mcp-server-gemini` 的代码，并在您的本地启动这个服务器。
> *   服务器启动后会监听一个端口（如 `3005`），这个终端窗口需要保持运行状态才能让插件持续工作。

**第2步：配置 `gemini-cli` 连接到该服务器**

现在服务器已经运行了，我们需要告诉 `gemini-cli` 它的存在。这需要修改 `gemini-cli` 的配置文件。

1.  找到 `gemini-cli` 的配置文件。它通常位于您的用户主目录下的 `.config/gemini/` 文件夹中，文件名为 `settings.json`。
2.  打开 `settings.json` 文件，在其中添加 `mcpServers` 配置项，如下所示：

    ```json
    {
      "mcpServers": {
        "gemini-server": {
          "command": "npx",
          "args": ["-y", "github:aliargun/mcp-server-gemini"],
          "env": {
            "GEMINI_API_KEY": "在这里再次粘贴你的API密钥"
          }
        }
      }
    }
    ```

> **配置解读**：
> *   `gemini-server`: 这是你为这个服务器起的名字，可以自定义。
> *   `command` 和 `args`: 定义了 `gemini-cli` 在需要时如何自动启动这个服务器。
> *   `env`: 为这个服务器进程设置必要的环境变量。

**第3步：验证与使用**

1.  **重启 `gemini-cli`** 以加载新的配置。
2.  运行 `/mcp` 命令。如果配置成功，您现在应该能看到名为 `gemini-server` 的服务器以及它提供的工具。
3.  现在，您就可以在对话中通过自然语言使用这些新工具了。

这个例子展示了添加一个MCP工具的完整流程。您可以按照类似的方法，连接到其他任何遵循MCP协议的服务器，从而不断扩展 `gemini-cli` 的能力。 